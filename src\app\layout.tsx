import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'TB Website - Interactive 3D Experience',
  description: 'A modern website featuring interactive 3D models and engaging user experience',
  keywords: ['3D', 'interactive', 'modern', 'website', 'React', 'Next.js'],
  authors: [{ name: '<PERSON>' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
