'use client'

import { useRef, useEffect } from 'react'
import { use<PERSON>rame, useLoader } from '@react-three/fiber'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { useGLTF, OrbitControls } from '@react-three/drei'
import * as THREE from 'three'

interface Model3DProps {
  modelPath: string
  autoRotate?: boolean
  scale?: number
  position?: [number, number, number]
}

function Model({ modelPath, autoRotate = true, scale = 1, position = [0, 0, 0] }: Model3DProps) {
  const meshRef = useRef<THREE.Group>(null)
  
  // Load the GLTF model
  const { scene } = useGLTF(modelPath)
  
  // Auto-rotate the model
  useFrame((state, delta) => {
    if (meshRef.current && autoRotate) {
      meshRef.current.rotation.y += delta * 0.5
    }
  })

  // Clone the scene to avoid issues with multiple instances
  const clonedScene = scene.clone()

  return (
    <group ref={meshRef} position={position} scale={scale}>
      <primitive object={clonedScene} />
    </group>
  )
}

export default function Model3D({ 
  modelPath = '/models/tb.glb', 
  autoRotate = true, 
  scale = 1,
  position = [0, 0, 0]
}: Model3DProps) {
  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -10]} intensity={0.3} />
      
      <Model 
        modelPath={modelPath}
        autoRotate={autoRotate}
        scale={scale}
        position={position}
      />
      
      <OrbitControls 
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        autoRotate={false}
        autoRotateSpeed={0.5}
        minDistance={3}
        maxDistance={20}
      />
    </>
  )
}

// Preload the model
useGLTF.preload('/models/tb.glb')
