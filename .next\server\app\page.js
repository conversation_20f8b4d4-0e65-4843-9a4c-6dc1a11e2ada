/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cscripts%5Ctb2-test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cscripts%5Ctb2-test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cscripts%5Ctb2-test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cscripts%5Ctb2-test%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3NjcmlwdHMlNUN0YjItdGVzdCU1Q3NyYyU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RiLXdlYnNpdGUvPzI3NGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxzY3JpcHRzXFxcXHRiMi10ZXN0XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Features */ \"(ssr)/./src/components/Features.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Testimonials */ \"(ssr)/./src/components/Testimonials.tsx\");\n/* harmony import */ var _components_CTA__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CTA */ \"(ssr)/./src/components/CTA.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTA__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDSjtBQUNRO0FBQ1E7QUFDbEI7QUFDTTtBQUV6QixTQUFTTTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTs7MEJBQ2QsOERBQUNSLDBEQUFNQTs7Ozs7MEJBQ1AsOERBQUNDLHdEQUFJQTs7Ozs7MEJBQ0wsOERBQUNDLDREQUFRQTs7Ozs7MEJBQ1QsOERBQUNDLGdFQUFZQTs7Ozs7MEJBQ2IsOERBQUNDLHVEQUFHQTs7Ozs7MEJBQ0osOERBQUNDLDBEQUFNQTs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL3RiLXdlYnNpdGUvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcidcbmltcG9ydCBIZXJvIGZyb20gJ0AvY29tcG9uZW50cy9IZXJvJ1xuaW1wb3J0IEZlYXR1cmVzIGZyb20gJ0AvY29tcG9uZW50cy9GZWF0dXJlcydcbmltcG9ydCBUZXN0aW1vbmlhbHMgZnJvbSAnQC9jb21wb25lbnRzL1Rlc3RpbW9uaWFscydcbmltcG9ydCBDVEEgZnJvbSAnQC9jb21wb25lbnRzL0NUQSdcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL0Zvb3RlcidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cbiAgICAgIDxIZWFkZXIgLz5cbiAgICAgIDxIZXJvIC8+XG4gICAgICA8RmVhdHVyZXMgLz5cbiAgICAgIDxUZXN0aW1vbmlhbHMgLz5cbiAgICAgIDxDVEEgLz5cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L21haW4+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJIZWFkZXIiLCJIZXJvIiwiRmVhdHVyZXMiLCJUZXN0aW1vbmlhbHMiLCJDVEEiLCJGb290ZXIiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CTA.tsx":
/*!********************************!*\
  !*** ./src/components/CTA.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTA)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CTA() {\n    const handleGetStarted = ()=>{\n        // You can implement your signup/contact logic here\n        alert(\"Thank you for your interest! We'll be in touch soon.\");\n    };\n    const handleContact = ()=>{\n        const element = document.querySelector(\"#contact\");\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"py-24 bg-gradient-to-r from-primary-600 to-purple-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                            children: \"Ready to Get Started?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-xl text-blue-100 max-w-2xl mx-auto\",\n                            children: \"Join us today and transform your digital presence with cutting-edge 3D technology and modern web design that captivates your audience.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-12 flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGetStarted,\n                            className: \"group bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center\",\n                            children: [\n                                \"Sign Up Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleContact,\n                            className: \"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200\",\n                            children: \"Learn More\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-2\",\n                                    children: \"Email Us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-2\",\n                                    children: \"Call Us\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: \"+****************\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white mb-2\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: \"Free consultation available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mb-6\",\n                            children: \"Trusted by leading companies worldwide\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center space-x-8 opacity-60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"TechCorp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"InnovateLab\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"DesignCo\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"StartupX\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\CTA.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DVEEudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXNDO0FBQ2dCO0FBRXZDLFNBQVNJO0lBQ3RCLE1BQU1DLG1CQUFtQjtRQUN2QixtREFBbUQ7UUFDbkRDLE1BQU07SUFDUjtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQixNQUFNQyxVQUFVQyxTQUFTQyxhQUFhLENBQUM7UUFDdkMsSUFBSUYsU0FBUztZQUNYQSxRQUFRRyxjQUFjLENBQUM7Z0JBQUVDLFVBQVU7WUFBUztRQUM5QztJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQVFDLElBQUc7UUFBUUMsV0FBVTtrQkFDNUIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDZixpREFBTUEsQ0FBQ2dCLEdBQUc7b0JBQ1RDLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxhQUFhO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUNoQ0UsWUFBWTt3QkFBRUMsVUFBVTtvQkFBSTtvQkFDNUJDLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7b0JBQ3ZCVCxXQUFVOztzQ0FFViw4REFBQ1U7NEJBQUdWLFdBQVU7c0NBQTJEOzs7Ozs7c0NBR3pFLDhEQUFDVzs0QkFBRVgsV0FBVTtzQ0FBK0M7Ozs7Ozs7Ozs7Ozs4QkFNOUQsOERBQUNmLGlEQUFNQSxDQUFDZ0IsR0FBRztvQkFDVEMsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLGFBQWE7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQ2hDRSxZQUFZO3dCQUFFQyxVQUFVO3dCQUFLSyxPQUFPO29CQUFJO29CQUN4Q0osVUFBVTt3QkFBRUMsTUFBTTtvQkFBSztvQkFDdkJULFdBQVU7O3NDQUVWLDhEQUFDYTs0QkFDQ0MsU0FBU3hCOzRCQUNUVSxXQUFVOztnQ0FDWDs4Q0FFQyw4REFBQ2QsaUdBQVVBO29DQUFDYyxXQUFVOzs7Ozs7Ozs7Ozs7c0NBR3hCLDhEQUFDYTs0QkFDQ0MsU0FBU3RCOzRCQUNUUSxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7OEJBTUgsOERBQUNmLGlEQUFNQSxDQUFDZ0IsR0FBRztvQkFDVEMsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLGFBQWE7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQ2hDRSxZQUFZO3dCQUFFQyxVQUFVO3dCQUFLSyxPQUFPO29CQUFJO29CQUN4Q0osVUFBVTt3QkFBRUMsTUFBTTtvQkFBSztvQkFDdkJULFdBQVU7O3NDQUVWLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDYixpR0FBSUE7d0NBQUNhLFdBQVU7Ozs7Ozs7Ozs7OzhDQUVsQiw4REFBQ2U7b0NBQUdmLFdBQVU7OENBQXdDOzs7Ozs7OENBQ3RELDhEQUFDVztvQ0FBRVgsV0FBVTs4Q0FBZ0I7Ozs7Ozs7Ozs7OztzQ0FHL0IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNaLGlHQUFLQTt3Q0FBQ1ksV0FBVTs7Ozs7Ozs7Ozs7OENBRW5CLDhEQUFDZTtvQ0FBR2YsV0FBVTs4Q0FBd0M7Ozs7Ozs4Q0FDdEQsOERBQUNXO29DQUFFWCxXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7O3NDQUcvQiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ2QsaUdBQVVBO3dDQUFDYyxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFeEIsOERBQUNlO29DQUFHZixXQUFVOzhDQUF3Qzs7Ozs7OzhDQUN0RCw4REFBQ1c7b0NBQUVYLFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS2pDLDhEQUFDZixpREFBTUEsQ0FBQ2dCLEdBQUc7b0JBQ1RDLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxhQUFhO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUNoQ0UsWUFBWTt3QkFBRUMsVUFBVTt3QkFBS0ssT0FBTztvQkFBSTtvQkFDeENKLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7b0JBQ3ZCVCxXQUFVOztzQ0FFViw4REFBQ1c7NEJBQUVYLFdBQVU7c0NBQXFCOzs7Ozs7c0NBQ2xDLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBRWIsOERBQUNDO29DQUFJRCxXQUFVOzhDQUErQjs7Ozs7OzhDQUM5Qyw4REFBQ0M7b0NBQUlELFdBQVU7OENBQStCOzs7Ozs7OENBQzlDLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FBK0I7Ozs7Ozs4Q0FDOUMsOERBQUNDO29DQUFJRCxXQUFVOzhDQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNMUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90Yi13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvQ1RBLnRzeD9lM2E5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgQXJyb3dSaWdodCwgTWFpbCwgUGhvbmUgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENUQSgpIHtcbiAgY29uc3QgaGFuZGxlR2V0U3RhcnRlZCA9ICgpID0+IHtcbiAgICAvLyBZb3UgY2FuIGltcGxlbWVudCB5b3VyIHNpZ251cC9jb250YWN0IGxvZ2ljIGhlcmVcbiAgICBhbGVydCgnVGhhbmsgeW91IGZvciB5b3VyIGludGVyZXN0ISBXZVxcJ2xsIGJlIGluIHRvdWNoIHNvb24uJylcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUNvbnRhY3QgPSAoKSA9PiB7XG4gICAgY29uc3QgZWxlbWVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJyNjb250YWN0JylcbiAgICBpZiAoZWxlbWVudCkge1xuICAgICAgZWxlbWVudC5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gaWQ9XCJhYm91dFwiIGNsYXNzTmFtZT1cInB5LTI0IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5LTYwMCB0by1wdXJwbGUtNzAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIlxuICAgICAgICA+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCB0ZXh0LXdoaXRlIHNtOnRleHQtNHhsXCI+XG4gICAgICAgICAgICBSZWFkeSB0byBHZXQgU3RhcnRlZD9cbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC14bCB0ZXh0LWJsdWUtMTAwIG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICBKb2luIHVzIHRvZGF5IGFuZCB0cmFuc2Zvcm0geW91ciBkaWdpdGFsIHByZXNlbmNlIHdpdGggY3V0dGluZy1lZGdlIDNEIHRlY2hub2xvZ3kgXG4gICAgICAgICAgICBhbmQgbW9kZXJuIHdlYiBkZXNpZ24gdGhhdCBjYXB0aXZhdGVzIHlvdXIgYXVkaWVuY2UuXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xMiBmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlclwiXG4gICAgICAgID5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVHZXRTdGFydGVkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgYmctd2hpdGUgdGV4dC1wcmltYXJ5LTYwMCBweC04IHB5LTQgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBTaWduIFVwIE5vd1xuICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwibWwtMiB3LTUgaC01IGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICBcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDb250YWN0fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLTIgYm9yZGVyLXdoaXRlIHRleHQtd2hpdGUgcHgtOCBweS00IHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCBob3ZlcjpiZy13aGl0ZSBob3Zlcjp0ZXh0LXByaW1hcnktNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgTGVhcm4gTW9yZVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIEFkZGl0aW9uYWwgaW5mbyBzZWN0aW9uICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC40IH19XG4gICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTE2IGdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLThcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCByb3VuZGVkLWZ1bGwgdy0xNiBoLTE2IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPkVtYWlsIFVzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDBcIj5oZWxsb0B0YndlYnNpdGUuY29tPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCByb3VuZGVkLWZ1bGwgdy0xNiBoLTE2IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5DYWxsIFVzPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDBcIj4rMSAoNTU1KSAxMjMtNDU2NzwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvMTAgcm91bmRlZC1mdWxsIHctMTYgaC0xNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5HZXQgU3RhcnRlZDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwXCI+RnJlZSBjb25zdWx0YXRpb24gYXZhaWxhYmxlPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIFRydXN0IGluZGljYXRvcnMgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjYgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtMTYgdGV4dC1jZW50ZXJcIlxuICAgICAgICA+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTEwMCBtYi02XCI+VHJ1c3RlZCBieSBsZWFkaW5nIGNvbXBhbmllcyB3b3JsZHdpZGU8L3A+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBzcGFjZS14LTggb3BhY2l0eS02MFwiPlxuICAgICAgICAgICAgey8qIFlvdSBjYW4gcmVwbGFjZSB0aGVzZSB3aXRoIGFjdHVhbCBjb21wYW55IGxvZ29zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+VGVjaENvcnA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1sZ1wiPklubm92YXRlTGFiPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj5EZXNpZ25DbzwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+U3RhcnR1cFg8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApXG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiQXJyb3dSaWdodCIsIk1haWwiLCJQaG9uZSIsIkNUQSIsImhhbmRsZUdldFN0YXJ0ZWQiLCJhbGVydCIsImhhbmRsZUNvbnRhY3QiLCJlbGVtZW50IiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsInNlY3Rpb24iLCJpZCIsImNsYXNzTmFtZSIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwiaDIiLCJwIiwiZGVsYXkiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CTA.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Palette,Settings,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Palette,Settings,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Palette,Settings,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst features = [\n    {\n        name: \"Fast & Responsive\",\n        description: \"Optimized for all devices with lightning-fast performance and smooth interactions.\",\n        icon: _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    },\n    {\n        name: \"Interactive 3D\",\n        description: \"Engaging user experience with interactive 3D models and immersive animations.\",\n        icon: _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    {\n        name: \"Easy to Customize\",\n        description: \"Built with React and modern technologies for maximum flexibility and customization.\",\n        icon: _barrel_optimize_names_Palette_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nfunction Features() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-24 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"Why Choose Our Platform?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"We combine cutting-edge technology with intuitive design to deliver exceptional user experiences that engage and inspire.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n                    children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            className: \"relative group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-6 group-hover:bg-primary-200 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                            className: \"w-6 h-6 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: feature.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-primary-500 to-purple-600 rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, feature.name, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-20 grid grid-cols-2 gap-8 md:grid-cols-4\",\n                    children: [\n                        {\n                            label: \"Projects Completed\",\n                            value: \"100+\"\n                        },\n                        {\n                            label: \"Happy Clients\",\n                            value: \"50+\"\n                        },\n                        {\n                            label: \"Years Experience\",\n                            value: \"5+\"\n                        },\n                        {\n                            label: \"Technologies Used\",\n                            value: \"20+\"\n                        }\n                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Features.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Features.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst navigation = {\n    main: [\n        {\n            name: \"Home\",\n            href: \"#home\"\n        },\n        {\n            name: \"Features\",\n            href: \"#features\"\n        },\n        {\n            name: \"About\",\n            href: \"#about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"#contact\"\n        }\n    ],\n    social: [\n        {\n            name: \"GitHub\",\n            href: \"#\",\n            icon: _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n        },\n        {\n            name: \"Twitter\",\n            href: \"#\",\n            icon: _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            name: \"LinkedIn\",\n            href: \"#\",\n            icon: _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"Email\",\n            href: \"mailto:<EMAIL>\",\n            icon: _barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        }\n    ]\n};\nfunction Footer() {\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const scrollToSection = (href)=>{\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        id: \"contact\",\n        className: \"bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-white mb-4\",\n                                        children: \"TB Website\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-6 max-w-md\",\n                                        children: \"Creating immersive 3D web experiences that engage, inspire, and convert. Built with modern technologies for the future of web design.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: navigation.main.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>scrollToSection(item.href),\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, item.name, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-semibold text-gray-400 tracking-wider uppercase mb-4\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"tel:+15551234567\",\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: \"+****************\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Available 24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" TB Website. All rights reserved. Built with ❤️ by Aziz Khan.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: scrollToTop,\n                            className: \"mt-4 md:mt-0 bg-primary-600 hover:bg-primary-700 text-white p-2 rounded-full transition-colors duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"#home\"\n    },\n    {\n        name: \"Features\",\n        href: \"#features\"\n    },\n    {\n        name: \"About\",\n        href: \"#about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"#contact\"\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const scrollToSection = (href)=>{\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n        setMobileMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center justify-between p-6 lg:px-8\",\n                \"aria-label\": \"Global\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"-m-1.5 p-1.5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-gradient\",\n                                children: \"TB\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                            onClick: ()=>setMobileMenuOpen(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Open main menu\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:gap-x-12\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(item.href),\n                                className: \"text-sm font-semibold leading-6 text-gray-900 hover:text-primary-600 transition-colors duration-200\",\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>scrollToSection(\"#contact\"),\n                            className: \"bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-primary-700 transition-colors duration-200\",\n                            children: \"Get Started\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: \"100%\"\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: \"100%\"\n                },\n                className: \"lg:hidden fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"-m-1.5 p-1.5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gradient\",\n                                    children: \"TB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                onClick: ()=>setMobileMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Close menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flow-root\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"-my-6 divide-y divide-gray-500/10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 py-6\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>scrollToSection(item.href),\n                                            className: \"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50 w-full text-left\",\n                                            children: item.name\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection(\"#contact\"),\n                                        className: \"bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-primary-700 transition-colors duration-200 w-full\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Model3D__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Model3D */ \"(ssr)/./src/components/Model3D.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n        }, void 0, false, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\nfunction Hero() {\n    const scrollToFeatures = ()=>{\n        const element = document.querySelector(\"#features\");\n        if (element) {\n            element.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 hero-gradient opacity-90\"\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"text-center lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold text-white mb-6\",\n                                    children: [\n                                        \"Welcome to Our\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-yellow-300\",\n                                            children: \"3D World\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-200 mb-8 max-w-lg\",\n                                    children: \"Experience the future of web design with interactive 3D models, smooth animations, and cutting-edge technology.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: scrollToFeatures,\n                                            className: \"bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>document.querySelector(\"#about\")?.scrollIntoView({\n                                                    behavior: \"smooth\"\n                                                }),\n                                            className: \"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200\",\n                                            children: \"Learn More\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"h-96 lg:h-[500px] w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.Canvas, {\n                                camera: {\n                                    position: [\n                                        0,\n                                        0,\n                                        5\n                                    ],\n                                    fov: 75\n                                },\n                                className: \"rounded-lg shadow-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: null,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Model3D__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        modelPath: \"/models/tb.glb\",\n                                        autoRotate: true,\n                                        scale: 1,\n                                        position: [\n                                            0,\n                                            0,\n                                            0\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    delay: 1\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Model3D.tsx":
/*!************************************!*\
  !*** ./src/components/Model3D.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Model3D)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/Gltf.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/drei */ \"(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Model({ modelPath, autoRotate = true, scale = 1, position = [\n    0,\n    0,\n    0\n] }) {\n    const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load the GLTF model\n    const { scene } = (0,_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF)(modelPath);\n    // Auto-rotate the model\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)((state, delta)=>{\n        if (meshRef.current && autoRotate) {\n            meshRef.current.rotation.y += delta * 0.5;\n        }\n    });\n    // Clone the scene to avoid issues with multiple instances\n    const clonedScene = scene.clone();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"group\", {\n        ref: meshRef,\n        position: position,\n        scale: scale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n            object: clonedScene\n        }, void 0, false, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction Model3D({ modelPath = \"/models/tb.glb\", autoRotate = true, scale = 1, position = [\n    0,\n    0,\n    0\n] }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ambientLight\", {\n                intensity: 0.5\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"directionalLight\", {\n                position: [\n                    10,\n                    10,\n                    5\n                ],\n                intensity: 1,\n                castShadow: true,\n                \"shadow-mapSize-width\": 2048,\n                \"shadow-mapSize-height\": 2048\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pointLight\", {\n                position: [\n                    -10,\n                    -10,\n                    -10\n                ],\n                intensity: 0.3\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Model, {\n                modelPath: modelPath,\n                autoRotate: autoRotate,\n                scale: scale,\n                position: position\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_4__.OrbitControls, {\n                enablePan: true,\n                enableZoom: true,\n                enableRotate: true,\n                autoRotate: false,\n                autoRotateSpeed: 0.5,\n                minDistance: 3,\n                maxDistance: 20\n            }, void 0, false, {\n                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Model3D.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n// Preload the model\n_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.useGLTF.preload(\"/models/tb.glb\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Model3D.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Johnson\",\n        role: \"CEO, TechStart\",\n        avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80\",\n        content: \"The 3D interactive experience completely transformed our website. Our engagement rates increased by 300% and customers love the immersive experience.\",\n        rating: 5\n    },\n    {\n        id: 2,\n        name: \"Michael Chen\",\n        role: \"Creative Director, DesignCo\",\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n        content: \"Outstanding work! The attention to detail and smooth animations make this one of the best websites we've ever worked with. Highly recommended.\",\n        rating: 5\n    },\n    {\n        id: 3,\n        name: \"Emily Rodriguez\",\n        role: \"Marketing Manager, InnovateLab\",\n        avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80\",\n        content: \"The modern design and interactive elements perfectly represent our brand. Our clients are impressed and our conversion rates have significantly improved.\",\n        rating: 5\n    }\n];\nfunction Testimonials() {\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const nextTestimonial = ()=>{\n        setCurrentIndex((prevIndex)=>(prevIndex + 1) % testimonials.length);\n    };\n    const prevTestimonial = ()=>{\n        setCurrentIndex((prevIndex)=>(prevIndex - 1 + testimonials.length) % testimonials.length);\n    };\n    // Auto-advance testimonials\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(nextTestimonial, 5000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"What Our Clients Say\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Don't just take our word for it. Here's what our satisfied clients have to say about their experience.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                className: \"bg-gray-50 rounded-2xl p-8 md:p-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        children: [\n                                            ...Array(testimonials[currentIndex].rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-yellow-400 fill-current\"\n                                            }, i, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                        className: \"text-xl md:text-2xl text-gray-900 text-center mb-8 leading-relaxed\",\n                                        children: [\n                                            '\"',\n                                            testimonials[currentIndex].content,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"w-12 h-12 rounded-full mr-4\",\n                                                src: testimonials[currentIndex].avatar,\n                                                alt: testimonials[currentIndex].name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: testimonials[currentIndex].name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-600\",\n                                                        children: testimonials[currentIndex].role\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, currentIndex, true, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: prevTestimonial,\n                            className: \"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: nextTestimonial,\n                            className: \"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 space-x-2\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentIndex(index),\n                                    className: `w-3 h-3 rounded-full transition-colors duration-200 ${index === currentIndex ? \"bg-primary-600\" : \"bg-gray-300\"}`\n                                }, index, false, {\n                                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"63aa050132d3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGItd2Vic2l0ZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YjkyOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYzYWEwNTAxMzJkM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"TB Website - Interactive 3D Experience\",\n    description: \"A modern website featuring interactive 3D models and engaging user experience\",\n    keywords: [\n        \"3D\",\n        \"interactive\",\n        \"modern\",\n        \"website\",\n        \"React\",\n        \"Next.js\"\n    ],\n    authors: [\n        {\n            name: \"Aziz Khan\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\scripts\\\\tb2-test\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFNO1FBQWU7UUFBVTtRQUFXO1FBQVM7S0FBVTtJQUN4RUMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBWTtLQUFFO0lBQ2hDQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXYiwrSkFBZTtzQkFDN0JTOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGItd2Vic2l0ZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnVEIgV2Vic2l0ZSAtIEludGVyYWN0aXZlIDNEIEV4cGVyaWVuY2UnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHdlYnNpdGUgZmVhdHVyaW5nIGludGVyYWN0aXZlIDNEIG1vZGVscyBhbmQgZW5nYWdpbmcgdXNlciBleHBlcmllbmNlJyxcbiAga2V5d29yZHM6IFsnM0QnLCAnaW50ZXJhY3RpdmUnLCAnbW9kZXJuJywgJ3dlYnNpdGUnLCAnUmVhY3QnLCAnTmV4dC5qcyddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnQXppeiBLaGFuJyB9XSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\scripts\tb2-test\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/three-stdlib","vendor-chunks/@react-three","vendor-chunks/@swc","vendor-chunks/react-reconciler","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/three","vendor-chunks/react-use-measure","vendor-chunks/its-fine","vendor-chunks/@babel","vendor-chunks/suspend-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cscripts%5Ctb2-test%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cscripts%5Ctb2-test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();