"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@babel";
exports.ids = ["vendor-chunks/@babel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/extends.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _extends)\n/* harmony export */ });\nfunction _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function(n) {\n        for(var e = 1; e < arguments.length; e++){\n            var t = arguments[e];\n            for(var r in t)({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n        }\n        return n;\n    }, _extends.apply(null, arguments);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0E7SUFDUCxPQUFPQSxXQUFXQyxPQUFPQyxNQUFNLEdBQUdELE9BQU9DLE1BQU0sQ0FBQ0MsSUFBSSxLQUFLLFNBQVVDLENBQUM7UUFDbEUsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlDLFVBQVVDLE1BQU0sRUFBRUYsSUFBSztZQUN6QyxJQUFJRyxJQUFJRixTQUFTLENBQUNELEVBQUU7WUFDcEIsSUFBSyxJQUFJSSxLQUFLRCxFQUFHLENBQUMsQ0FBQyxHQUFHRSxjQUFjLENBQUNDLElBQUksQ0FBQ0gsR0FBR0MsTUFBT0wsQ0FBQUEsQ0FBQyxDQUFDSyxFQUFFLEdBQUdELENBQUMsQ0FBQ0MsRUFBRTtRQUNqRTtRQUNBLE9BQU9MO0lBQ1QsR0FBR0osU0FBU1ksS0FBSyxDQUFDLE1BQU1OO0FBQzFCO0FBQytCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGItd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzLmpzPzhlYzIiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gIHJldHVybiBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gPyBPYmplY3QuYXNzaWduLmJpbmQoKSA6IGZ1bmN0aW9uIChuKSB7XG4gICAgZm9yICh2YXIgZSA9IDE7IGUgPCBhcmd1bWVudHMubGVuZ3RoOyBlKyspIHtcbiAgICAgIHZhciB0ID0gYXJndW1lbnRzW2VdO1xuICAgICAgZm9yICh2YXIgciBpbiB0KSAoe30pLmhhc093blByb3BlcnR5LmNhbGwodCwgcikgJiYgKG5bcl0gPSB0W3JdKTtcbiAgICB9XG4gICAgcmV0dXJuIG47XG4gIH0sIF9leHRlbmRzLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG59XG5leHBvcnQgeyBfZXh0ZW5kcyBhcyBkZWZhdWx0IH07Il0sIm5hbWVzIjpbIl9leHRlbmRzIiwiT2JqZWN0IiwiYXNzaWduIiwiYmluZCIsIm4iLCJlIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidCIsInIiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJhcHBseSIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\n");

/***/ })

};
;