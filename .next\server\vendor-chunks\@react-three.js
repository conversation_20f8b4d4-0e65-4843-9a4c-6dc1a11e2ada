"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/drei/core/Clone.js":
/*!******************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Clone.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Clone: () => (/* binding */ Clone)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/utils/SkeletonUtils.js\");\n\n\n\n\nfunction createSpread(child, { keys = [\n    \"near\",\n    \"far\",\n    \"color\",\n    \"distance\",\n    \"decay\",\n    \"penumbra\",\n    \"angle\",\n    \"intensity\",\n    \"skeleton\",\n    \"visible\",\n    \"castShadow\",\n    \"receiveShadow\",\n    \"morphTargetDictionary\",\n    \"morphTargetInfluences\",\n    \"name\",\n    \"geometry\",\n    \"material\",\n    \"position\",\n    \"rotation\",\n    \"scale\",\n    \"up\",\n    \"userData\",\n    \"bindMode\",\n    \"bindMatrix\",\n    \"bindMatrixInverse\",\n    \"skeleton\"\n], deep, inject, castShadow, receiveShadow }) {\n    let spread = {};\n    for (const key of keys){\n        spread[key] = child[key];\n    }\n    if (deep) {\n        if (spread.geometry && deep !== \"materialsOnly\") spread.geometry = spread.geometry.clone();\n        if (spread.material && deep !== \"geometriesOnly\") spread.material = spread.material.clone();\n    }\n    if (inject) {\n        if (typeof inject === \"function\") spread = {\n            ...spread,\n            children: inject(child)\n        };\n        else if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(inject)) spread = {\n            ...spread,\n            children: inject\n        };\n        else spread = {\n            ...spread,\n            ...inject\n        };\n    }\n    if (child instanceof three__WEBPACK_IMPORTED_MODULE_2__.Mesh) {\n        if (castShadow) spread.castShadow = true;\n        if (receiveShadow) spread.receiveShadow = true;\n    }\n    return spread;\n}\nconst Clone = /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ isChild = false, object, children, deep, castShadow, receiveShadow, inject, keys, ...props }, forwardRef)=>{\n    const config = {\n        keys,\n        deep,\n        inject,\n        castShadow,\n        receiveShadow\n    };\n    object = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>{\n        if (isChild === false && !Array.isArray(object)) {\n            let isSkinned = false;\n            object.traverse((object)=>{\n                if (object.isSkinnedMesh) isSkinned = true;\n            });\n            if (isSkinned) return three_stdlib__WEBPACK_IMPORTED_MODULE_3__.SkeletonUtils.clone(object);\n        }\n        return object;\n    }, [\n        object,\n        isChild\n    ]);\n    // Deal with arrayed clones\n    if (Array.isArray(object)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n            ref: forwardRef\n        }), object.map((o)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n                key: o.uuid,\n                object: o\n            }, config))), children);\n    }\n    // Singleton clones\n    const { children: injectChildren, ...spread } = createSpread(object, config);\n    const Element = object.type[0].toLowerCase() + object.type.slice(1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Element, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, spread, props, {\n        ref: forwardRef\n    }), object.children.map((child)=>{\n        if (child.type === \"Bone\") return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            key: child.uuid,\n            object: child\n        }, config));\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            key: child.uuid,\n            object: child\n        }, config, {\n            isChild: true\n        }));\n    }), children, injectChildren);\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Clone.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/Gltf.js":
/*!*****************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Gltf.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Gltf: () => (/* binding */ Gltf),\n/* harmony export */   useGLTF: () => (/* binding */ useGLTF)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/DRACOLoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/libs/MeshoptDecoder.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/GLTFLoader.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var _Clone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Clone.js */ \"(ssr)/./node_modules/@react-three/drei/core/Clone.js\");\n\n\n\n\n\nlet dracoLoader = null;\nlet decoderPath = \"https://www.gstatic.com/draco/versioned/decoders/1.5.5/\";\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n    return (loader)=>{\n        if (extendLoader) {\n            extendLoader(loader);\n        }\n        if (useDraco) {\n            if (!dracoLoader) {\n                dracoLoader = new three_stdlib__WEBPACK_IMPORTED_MODULE_2__.DRACOLoader();\n            }\n            dracoLoader.setDecoderPath(typeof useDraco === \"string\" ? useDraco : decoderPath);\n            loader.setDRACOLoader(dracoLoader);\n        }\n        if (useMeshopt) {\n            loader.setMeshoptDecoder(typeof three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder === \"function\" ? (0,three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder)() : three_stdlib__WEBPACK_IMPORTED_MODULE_3__.MeshoptDecoder);\n        }\n    };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader)=>(0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F)(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader)=>_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F.preload(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = (path)=>_react_three_fiber__WEBPACK_IMPORTED_MODULE_4__.F.clear(three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GLTFLoader, path);\nuseGLTF.setDecoderPath = (path)=>{\n    decoderPath = path;\n};\n//\nconst Gltf = /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ src, useDraco, useMeshOpt, extendLoader, ...props }, ref)=>{\n    const { scene } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Clone_js__WEBPACK_IMPORTED_MODULE_6__.Clone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: ref\n    }, props, {\n        object: scene\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Gltf.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js":
/*!**************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/OrbitControls.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrbitControls: () => (/* binding */ OrbitControls)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js\");\n\n\n\n\nconst OrbitControls = /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ makeDefault, camera, regress, domElement, enableDamping = true, keyEvents = false, onChange, onStart, onEnd, ...restProps }, ref)=>{\n    const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.invalidate);\n    const defaultCamera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.camera);\n    const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.gl);\n    const events = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.events);\n    const setEvents = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.setEvents);\n    const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.set);\n    const get = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.get);\n    const performance = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)((state)=>state.performance);\n    const explCamera = camera || defaultCamera;\n    const explDomElement = domElement || events.connected || gl.domElement;\n    const controls = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>new three_stdlib__WEBPACK_IMPORTED_MODULE_3__.OrbitControls(explCamera), [\n        explCamera\n    ]);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.C)(()=>{\n        if (controls.enabled) controls.update();\n    }, -1);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (keyEvents) {\n            controls.connect(keyEvents === true ? explDomElement : keyEvents);\n        }\n        controls.connect(explDomElement);\n        return ()=>void controls.dispose();\n    }, [\n        keyEvents,\n        explDomElement,\n        regress,\n        controls,\n        invalidate\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const callback = (e)=>{\n            invalidate();\n            if (regress) performance.regress();\n            if (onChange) onChange(e);\n        };\n        const onStartCb = (e)=>{\n            if (onStart) onStart(e);\n        };\n        const onEndCb = (e)=>{\n            if (onEnd) onEnd(e);\n        };\n        controls.addEventListener(\"change\", callback);\n        controls.addEventListener(\"start\", onStartCb);\n        controls.addEventListener(\"end\", onEndCb);\n        return ()=>{\n            controls.removeEventListener(\"start\", onStartCb);\n            controls.removeEventListener(\"end\", onEndCb);\n            controls.removeEventListener(\"change\", callback);\n        };\n    }, [\n        onChange,\n        onStart,\n        onEnd,\n        controls,\n        invalidate,\n        setEvents\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (makeDefault) {\n            const old = get().controls;\n            // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n            set({\n                controls\n            });\n            return ()=>set({\n                    controls: old\n                });\n        }\n    }, [\n        makeDefault,\n        controls\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: ref,\n        object: controls,\n        enableDamping: enableDamping\n    }, restProps));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useThree),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useFrame),\n/* harmony export */   D: () => (/* binding */ useGraph),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useLoader),\n/* harmony export */   _: () => (/* binding */ _roots),\n/* harmony export */   a: () => (/* binding */ useMutableCallback),\n/* harmony export */   b: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   c: () => (/* binding */ createRoot),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createPointerEvents),\n/* harmony export */   g: () => (/* binding */ createEvents),\n/* harmony export */   h: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ addEffect),\n/* harmony export */   k: () => (/* binding */ addAfterEffect),\n/* harmony export */   l: () => (/* binding */ addTail),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ createPortal),\n/* harmony export */   p: () => (/* binding */ context),\n/* harmony export */   q: () => (/* binding */ applyProps),\n/* harmony export */   r: () => (/* binding */ reconciler),\n/* harmony export */   s: () => (/* binding */ getRootState),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useBridge),\n/* harmony export */   v: () => (/* binding */ dispose),\n/* harmony export */   w: () => (/* binding */ act),\n/* harmony export */   x: () => (/* binding */ buildGraph),\n/* harmony export */   y: () => (/* binding */ useInstanceHandle),\n/* harmony export */   z: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand_traditional__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand/traditional */ \"(ssr)/./node_modules/zustand/esm/traditional.mjs\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/ Object.freeze({\n    __proto__: null\n});\n/**\r\n * Returns the instance's initial (outmost) root.\r\n */ function findInitialRoot(instance) {\n    let root = instance.root;\n    while(root.getState().previousRoot)root = root.getState().previousRoot;\n    return root;\n}\n/**\r\n * Safely flush async effects when testing, simulating a legacy root.\r\n * @deprecated Import from React instead. import { act } from 'react'\r\n */ // Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = react__WEBPACK_IMPORTED_MODULE_0__[\"act\" + \"\"];\nconst isOrthographicCamera = (def)=>def && def.isOrthographicCamera;\nconst isRef = (obj)=>obj && obj.hasOwnProperty(\"current\");\nconst isColorRepresentation = (value)=>value != null && (typeof value === \"string\" || typeof value === \"number\" || value.isColor);\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */ const useIsomorphicLayoutEffect = /* @__PURE__ */ ((_window$document, _window$navigator)=> false && (0))() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useIsomorphicLayoutEffect(()=>void (ref.current = fn), [\n        fn\n    ]);\n    return ref;\n}\n/**\r\n * Bridges renderer Context and StrictMode from a primary renderer.\r\n */ function useBridge() {\n    const fiber = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useFiber)();\n    const ContextBridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useContextBridge)();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({ children })=>{\n            const strict = !!(0,its_fine__WEBPACK_IMPORTED_MODULE_5__.traverseFiber)(fiber, true, (node)=>node.type === react__WEBPACK_IMPORTED_MODULE_0__.StrictMode);\n            const Root = strict ? react__WEBPACK_IMPORTED_MODULE_0__.StrictMode : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n            return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Root, {\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ContextBridge, {\n                    children: children\n                })\n            });\n        }, [\n        fiber,\n        ContextBridge\n    ]);\n}\nfunction Block({ set }) {\n    useIsomorphicLayoutEffect(()=>{\n        set(new Promise(()=>null));\n        return ()=>set(false);\n    }, [\n        set\n    ]);\n    return null;\n}\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */ ((_ErrorBoundary)=>(_ErrorBoundary = class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n        constructor(...args){\n            super(...args);\n            this.state = {\n                error: false\n            };\n        }\n        componentDidCatch(err) {\n            this.props.set(err);\n        }\n        render() {\n            return this.state.error ? null : this.props.children;\n        }\n    }, _ErrorBoundary.getDerivedStateFromError = ()=>({\n            error: true\n        }), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n    var _window$devicePixelRa;\n    // Err on the side of progress by assuming 2x dpr if we can't detect it\n    // This will happen in workers where window is defined but dpr isn't.\n    const target =  false ? 0 : 1;\n    return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n/**\r\n * Returns instance root state\r\n */ function getRootState(obj) {\n    var _r3f;\n    return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n    obj: (a)=>a === Object(a) && !is.arr(a) && typeof a !== \"function\",\n    fun: (a)=>typeof a === \"function\",\n    str: (a)=>typeof a === \"string\",\n    num: (a)=>typeof a === \"number\",\n    boo: (a)=>typeof a === \"boolean\",\n    und: (a)=>a === void 0,\n    nul: (a)=>a === null,\n    arr: (a)=>Array.isArray(a),\n    equ (a, b, { arrays = \"shallow\", objects = \"reference\", strict = true } = {}) {\n        // Wrong type or one of the two undefined, doesn't match\n        if (typeof a !== typeof b || !!a !== !!b) return false;\n        // Atomic, just compare a against b\n        if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n        const isObj = is.obj(a);\n        if (isObj && objects === \"reference\") return a === b;\n        const isArr = is.arr(a);\n        if (isArr && arrays === \"reference\") return a === b;\n        // Array or Object, shallow compare first to see if it's a match\n        if ((isArr || isObj) && a === b) return true;\n        // Last resort, go through keys\n        let i;\n        // Check if a has all the keys of b\n        for(i in a)if (!(i in b)) return false;\n        // Check if values between keys match\n        if (isObj && arrays === \"shallow\" && objects === \"shallow\") {\n            for(i in strict ? b : a)if (!is.equ(a[i], b[i], {\n                strict,\n                objects: \"reference\"\n            })) return false;\n        } else {\n            for(i in strict ? b : a)if (a[i] !== b[i]) return false;\n        }\n        // If i is undefined\n        if (is.und(i)) {\n            // If both arrays are empty we consider them equal\n            if (isArr && a.length === 0 && b.length === 0) return true;\n            // If both objects are empty we consider them equal\n            if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n            // Otherwise match them by value\n            if (a !== b) return false;\n        }\n        return true;\n    }\n};\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n    const data = {\n        nodes: {},\n        materials: {},\n        meshes: {}\n    };\n    if (object) {\n        object.traverse((obj)=>{\n            if (obj.name) data.nodes[obj.name] = obj;\n            if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n            if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n        });\n    }\n    return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n    if (obj.type !== \"Scene\") obj.dispose == null ? void 0 : obj.dispose();\n    for(const p in obj){\n        const prop = obj[p];\n        if ((prop == null ? void 0 : prop.type) !== \"Scene\") prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n    }\n}\nconst REACT_INTERNAL_PROPS = [\n    \"children\",\n    \"key\",\n    \"ref\"\n];\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n    const props = {};\n    for(const key in queue){\n        if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n    }\n    return props;\n}\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n    const object = target;\n    // Create instance descriptor\n    let instance = object == null ? void 0 : object.__r3f;\n    if (!instance) {\n        instance = {\n            root,\n            type,\n            parent: null,\n            children: [],\n            props: getInstanceProps(props),\n            object,\n            eventCount: 0,\n            handlers: {},\n            isHidden: false\n        };\n        if (object) object.__r3f = instance;\n    }\n    return instance;\n}\nfunction resolve(root, key) {\n    let target = root[key];\n    if (!key.includes(\"-\")) return {\n        root,\n        key,\n        target\n    };\n    // Resolve pierced target\n    target = root;\n    for (const part of key.split(\"-\")){\n        var _target;\n        key = part;\n        root = target;\n        target = (_target = target) == null ? void 0 : _target[key];\n    }\n    // TODO: change key to 'foo-bar' if target is undefined?\n    return {\n        root,\n        key,\n        target\n    };\n}\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n    if (is.str(child.props.attach)) {\n        // If attaching into an array (foo-0), create one\n        if (INDEX_REGEX.test(child.props.attach)) {\n            const index = child.props.attach.replace(INDEX_REGEX, \"\");\n            const { root, key } = resolve(parent.object, index);\n            if (!Array.isArray(root[key])) root[key] = [];\n        }\n        const { root, key } = resolve(parent.object, child.props.attach);\n        child.previousAttach = root[key];\n        root[key] = child.object;\n    } else if (is.fun(child.props.attach)) {\n        child.previousAttach = child.props.attach(parent.object, child.object);\n    }\n}\nfunction detach(parent, child) {\n    if (is.str(child.props.attach)) {\n        const { root, key } = resolve(parent.object, child.props.attach);\n        const previous = child.previousAttach;\n        // When the previous value was undefined, it means the value was never set to begin with\n        if (previous === undefined) delete root[key];\n        else root[key] = previous;\n    } else {\n        child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n    }\n    delete child.previousAttach;\n}\nconst RESERVED_PROPS = [\n    ...REACT_INTERNAL_PROPS,\n    // Instance props\n    \"args\",\n    \"dispose\",\n    \"attach\",\n    \"object\",\n    \"onUpdate\",\n    // Behavior flags\n    \"dispose\"\n];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n    let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n    try {\n        if (!ctor) {\n            ctor = new root.constructor();\n            MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n        }\n    } catch (e) {\n    // ...\n    }\n    return ctor;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n    const changedProps = {};\n    // Sort through props\n    for(const prop in newProps){\n        // Skip reserved keys\n        if (RESERVED_PROPS.includes(prop)) continue;\n        // Skip if props match\n        if (is.equ(newProps[prop], instance.props[prop])) continue;\n        // Props changed, add them\n        changedProps[prop] = newProps[prop];\n        // Reset pierced props\n        for(const other in newProps){\n            if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n        }\n    }\n    // Reset removed props for HMR\n    for(const prop in instance.props){\n        if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n        const { root, key } = resolve(instance.object, prop);\n        // https://github.com/mrdoob/three.js/issues/21209\n        // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n        // has no means to do this. Hence we curate a small collection of value-classes\n        // with their respective constructor/set arguments\n        // For removed props, try to set default values, if possible\n        if (root.constructor && root.constructor.length === 0) {\n            // create a blank slate of the instance and copy the particular parameter.\n            const ctor = getMemoizedPrototype(root);\n            if (!is.und(ctor)) changedProps[key] = ctor[key];\n        } else {\n            // instance does not have constructor, just set it to 0\n            changedProps[key] = 0;\n        }\n    }\n    return changedProps;\n}\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = [\n    \"map\",\n    \"emissiveMap\",\n    \"sheenColorMap\",\n    \"specularColorMap\",\n    \"envMap\"\n];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n    var _instance$object;\n    const instance = object.__r3f;\n    const rootState = instance && findInitialRoot(instance).getState();\n    const prevHandlers = instance == null ? void 0 : instance.eventCount;\n    for(const prop in props){\n        let value = props[prop];\n        // Don't mutate reserved keys\n        if (RESERVED_PROPS.includes(prop)) continue;\n        // Deal with pointer events, including removing them if undefined\n        if (instance && EVENT_REGEX.test(prop)) {\n            if (typeof value === \"function\") instance.handlers[prop] = value;\n            else delete instance.handlers[prop];\n            instance.eventCount = Object.keys(instance.handlers).length;\n            continue;\n        }\n        // Ignore setting undefined props\n        // https://github.com/pmndrs/react-three-fiber/issues/274\n        if (value === undefined) continue;\n        let { root, key, target } = resolve(object, prop);\n        // Layers must be written to the mask property\n        if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers) {\n            target.mask = value.mask;\n        } else if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Color && isColorRepresentation(value)) {\n            target.set(value);\n        } else if (target !== null && typeof target === \"object\" && typeof target.set === \"function\" && typeof target.copy === \"function\" && value != null && value.constructor && target.constructor === value.constructor) {\n            target.copy(value);\n        } else if (target !== null && typeof target === \"object\" && typeof target.set === \"function\" && Array.isArray(value)) {\n            if (typeof target.fromArray === \"function\") target.fromArray(value);\n            else target.set(...value);\n        } else if (target !== null && typeof target === \"object\" && typeof target.set === \"function\" && typeof value === \"number\") {\n            // Allow setting array scalars\n            if (typeof target.setScalar === \"function\") target.setScalar(value);\n            else target.set(value);\n        } else {\n            var _root$key;\n            root[key] = value;\n            // Auto-convert sRGB texture parameters for built-in materials\n            // https://github.com/pmndrs/react-three-fiber/issues/344\n            // https://github.com/mrdoob/three.js/pull/25857\n            if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture && // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n            root[key].format === three__WEBPACK_IMPORTED_MODULE_6__.RGBAFormat && root[key].type === three__WEBPACK_IMPORTED_MODULE_6__.UnsignedByteType) {\n                // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n                root[key].colorSpace = three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n            }\n        }\n    }\n    // Register event handlers\n    if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n        const object = instance.object;\n        // Pre-emptively remove the instance from the interaction manager\n        const index = rootState.internal.interaction.indexOf(object);\n        if (index > -1) rootState.internal.interaction.splice(index, 1);\n        // Add the instance to the interaction manager only when it has handlers\n        if (instance.eventCount && object.raycast !== null) {\n            rootState.internal.interaction.push(object);\n        }\n    }\n    // Auto-attach geometries and materials\n    if (instance && instance.props.attach === undefined) {\n        if (instance.object.isBufferGeometry) instance.props.attach = \"geometry\";\n        else if (instance.object.isMaterial) instance.props.attach = \"material\";\n    }\n    // Instance was updated, request a frame\n    if (instance) invalidateInstance(instance);\n    return object;\n}\nfunction invalidateInstance(instance) {\n    var _instance$root;\n    if (!instance.parent) return;\n    instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n    const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n    if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n    // Do not mess with the camera if it belongs to the user\n    // https://github.com/pmndrs/react-three-fiber/issues/92\n    if (camera.manual) return;\n    if (isOrthographicCamera(camera)) {\n        camera.left = size.width / -2;\n        camera.right = size.width / 2;\n        camera.top = size.height / 2;\n        camera.bottom = size.height / -2;\n    } else {\n        camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n}\nconst isObject3D = (object)=>object == null ? void 0 : object.isObject3D;\nfunction makeId(event) {\n    return (event.eventObject || event.object).uuid + \"/\" + event.index + event.instanceId;\n}\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */ function releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n    const captureData = captures.get(obj);\n    if (captureData) {\n        captures.delete(obj);\n        // If this was the last capturing object for this pointer\n        if (captures.size === 0) {\n            capturedMap.delete(pointerId);\n            captureData.target.releasePointerCapture(pointerId);\n        }\n    }\n}\nfunction removeInteractivity(store, object) {\n    const { internal } = store.getState();\n    // Removes every trace of an object from the data store\n    internal.interaction = internal.interaction.filter((o)=>o !== object);\n    internal.initialHits = internal.initialHits.filter((o)=>o !== object);\n    internal.hovered.forEach((value, key)=>{\n        if (value.eventObject === object || value.object === object) {\n            // Clear out intersects, they are outdated by now\n            internal.hovered.delete(key);\n        }\n    });\n    internal.capturedMap.forEach((captures, pointerId)=>{\n        releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n    });\n}\nfunction createEvents(store) {\n    /** Calculates delta */ function calculateDistance(event) {\n        const { internal } = store.getState();\n        const dx = event.offsetX - internal.initialClick[0];\n        const dy = event.offsetY - internal.initialClick[1];\n        return Math.round(Math.sqrt(dx * dx + dy * dy));\n    }\n    /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */ function filterPointerEvents(objects) {\n        return objects.filter((obj)=>[\n                \"Move\",\n                \"Over\",\n                \"Enter\",\n                \"Out\",\n                \"Leave\"\n            ].some((name)=>{\n                var _r3f;\n                return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers[\"onPointer\" + name];\n            }));\n    }\n    function intersect(event, filter) {\n        const state = store.getState();\n        const duplicates = new Set();\n        const intersections = [];\n        // Allow callers to eliminate event objects\n        const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n        // Reset all raycaster cameras to undefined\n        for(let i = 0; i < eventsObjects.length; i++){\n            const state = getRootState(eventsObjects[i]);\n            if (state) {\n                state.raycaster.camera = undefined;\n            }\n        }\n        if (!state.previousRoot) {\n            // Make sure root-level pointer and ray are set up\n            state.events.compute == null ? void 0 : state.events.compute(event, state);\n        }\n        function handleRaycast(obj) {\n            const state = getRootState(obj);\n            // Skip event handling when noEvents is set, or when the raycasters camera is null\n            if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n            // When the camera is undefined we have to call the event layers update function\n            if (state.raycaster.camera === undefined) {\n                var _state$previousRoot;\n                state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n                // If the camera is still undefined we have to skip this layer entirely\n                if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n            }\n            // Intersect object by object\n            return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n        }\n        // Collect events\n        let hits = eventsObjects// Intersect objects\n        .flatMap(handleRaycast)// Sort by event priority and distance\n        .sort((a, b)=>{\n            const aState = getRootState(a.object);\n            const bState = getRootState(b.object);\n            if (!aState || !bState) return a.distance - b.distance;\n            return bState.events.priority - aState.events.priority || a.distance - b.distance;\n        })// Filter out duplicates\n        .filter((item)=>{\n            const id = makeId(item);\n            if (duplicates.has(id)) return false;\n            duplicates.add(id);\n            return true;\n        });\n        // https://github.com/mrdoob/three.js/issues/16031\n        // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n        if (state.events.filter) hits = state.events.filter(hits, state);\n        // Bubble up the events, find the event source (eventObject)\n        for (const hit of hits){\n            let eventObject = hit.object;\n            // Bubble event up\n            while(eventObject){\n                var _r3f2;\n                if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n                    ...hit,\n                    eventObject\n                });\n                eventObject = eventObject.parent;\n            }\n        }\n        // If the interaction is captured, make all capturing targets part of the intersect.\n        if (\"pointerId\" in event && state.internal.capturedMap.has(event.pointerId)) {\n            for (let captureData of state.internal.capturedMap.get(event.pointerId).values()){\n                if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n            }\n        }\n        return intersections;\n    }\n    /**  Handles intersections by forwarding them to handlers */ function handleIntersects(intersections, event, delta, callback) {\n        // If anything has been found, forward it to the event listeners\n        if (intersections.length) {\n            const localState = {\n                stopped: false\n            };\n            for (const hit of intersections){\n                let state = getRootState(hit.object);\n                // If the object is not managed by R3F, it might be parented to an element which is.\n                // Traverse upwards until we find a managed parent and use its state instead.\n                if (!state) {\n                    hit.object.traverseAncestors((obj)=>{\n                        const parentState = getRootState(obj);\n                        if (parentState) {\n                            state = parentState;\n                            return false;\n                        }\n                    });\n                }\n                if (state) {\n                    const { raycaster, pointer, camera, internal } = state;\n                    const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n                    const hasPointerCapture = (id)=>{\n                        var _internal$capturedMap, _internal$capturedMap2;\n                        return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n                    };\n                    const setPointerCapture = (id)=>{\n                        const captureData = {\n                            intersection: hit,\n                            target: event.target\n                        };\n                        if (internal.capturedMap.has(id)) {\n                            // if the pointerId was previously captured, we add the hit to the\n                            // event capturedMap.\n                            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n                        } else {\n                            // if the pointerId was not previously captured, we create a map\n                            // containing the hitObject, and the hit. hitObject is used for\n                            // faster access.\n                            internal.capturedMap.set(id, new Map([\n                                [\n                                    hit.eventObject,\n                                    captureData\n                                ]\n                            ]));\n                        }\n                        event.target.setPointerCapture(id);\n                    };\n                    const releasePointerCapture = (id)=>{\n                        const captures = internal.capturedMap.get(id);\n                        if (captures) {\n                            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n                        }\n                    };\n                    // Add native event props\n                    let extractEventProps = {};\n                    // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n                    for(let prop in event){\n                        let property = event[prop];\n                        // Only copy over atomics, leave functions alone as these should be\n                        // called as event.nativeEvent.fn()\n                        if (typeof property !== \"function\") extractEventProps[prop] = property;\n                    }\n                    let raycastEvent = {\n                        ...hit,\n                        ...extractEventProps,\n                        pointer,\n                        intersections,\n                        stopped: localState.stopped,\n                        delta,\n                        unprojectedPoint,\n                        ray: raycaster.ray,\n                        camera: camera,\n                        // Hijack stopPropagation, which just sets a flag\n                        stopPropagation () {\n                            // https://github.com/pmndrs/react-three-fiber/issues/596\n                            // Events are not allowed to stop propagation if the pointer has been captured\n                            const capturesForPointer = \"pointerId\" in event && internal.capturedMap.get(event.pointerId);\n                            // We only authorize stopPropagation...\n                            if (// ...if this pointer hasn't been captured\n                            !capturesForPointer || // ... or if the hit object is capturing the pointer\n                            capturesForPointer.has(hit.eventObject)) {\n                                raycastEvent.stopped = localState.stopped = true;\n                                // Propagation is stopped, remove all other hover records\n                                // An event handler is only allowed to flush other handlers if it is hovered itself\n                                if (internal.hovered.size && Array.from(internal.hovered.values()).find((i)=>i.eventObject === hit.eventObject)) {\n                                    // Objects cannot flush out higher up objects that have already caught the event\n                                    const higher = intersections.slice(0, intersections.indexOf(hit));\n                                    cancelPointer([\n                                        ...higher,\n                                        hit\n                                    ]);\n                                }\n                            }\n                        },\n                        // there should be a distinction between target and currentTarget\n                        target: {\n                            hasPointerCapture,\n                            setPointerCapture,\n                            releasePointerCapture\n                        },\n                        currentTarget: {\n                            hasPointerCapture,\n                            setPointerCapture,\n                            releasePointerCapture\n                        },\n                        nativeEvent: event\n                    };\n                    // Call subscribers\n                    callback(raycastEvent);\n                    // Event bubbling may be interrupted by stopPropagation\n                    if (localState.stopped === true) break;\n                }\n            }\n        }\n        return intersections;\n    }\n    function cancelPointer(intersections) {\n        const { internal } = store.getState();\n        for (const hoveredObj of internal.hovered.values()){\n            // When no objects were hit or the the hovered object wasn't found underneath the cursor\n            // we call onPointerOut and delete the object from the hovered-elements map\n            if (!intersections.length || !intersections.find((hit)=>hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n                const eventObject = hoveredObj.eventObject;\n                const instance = eventObject.__r3f;\n                internal.hovered.delete(makeId(hoveredObj));\n                if (instance != null && instance.eventCount) {\n                    const handlers = instance.handlers;\n                    // Clear out intersects, they are outdated by now\n                    const data = {\n                        ...hoveredObj,\n                        intersections\n                    };\n                    handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n                    handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n                }\n            }\n        }\n    }\n    function pointerMissed(event, objects) {\n        for(let i = 0; i < objects.length; i++){\n            const instance = objects[i].__r3f;\n            instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n        }\n    }\n    function handlePointer(name) {\n        // Deal with cancelation\n        switch(name){\n            case \"onPointerLeave\":\n            case \"onPointerCancel\":\n                return ()=>cancelPointer([]);\n            case \"onLostPointerCapture\":\n                return (event)=>{\n                    const { internal } = store.getState();\n                    if (\"pointerId\" in event && internal.capturedMap.has(event.pointerId)) {\n                        // If the object event interface had onLostPointerCapture, we'd call it here on every\n                        // object that's getting removed. We call it on the next frame because onLostPointerCapture\n                        // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n                        // happen in the object it originated from, leaving components in a in-between state.\n                        requestAnimationFrame(()=>{\n                            // Only release if pointer-up didn't do it already\n                            if (internal.capturedMap.has(event.pointerId)) {\n                                internal.capturedMap.delete(event.pointerId);\n                                cancelPointer([]);\n                            }\n                        });\n                    }\n                };\n        }\n        // Any other pointer goes here ...\n        return function handleEvent(event) {\n            const { onPointerMissed, internal } = store.getState();\n            // prepareRay(event)\n            internal.lastEvent.current = event;\n            // Get fresh intersects\n            const isPointerMove = name === \"onPointerMove\";\n            const isClickEvent = name === \"onClick\" || name === \"onContextMenu\" || name === \"onDoubleClick\";\n            const filter = isPointerMove ? filterPointerEvents : undefined;\n            const hits = intersect(event, filter);\n            const delta = isClickEvent ? calculateDistance(event) : 0;\n            // Save initial coordinates on pointer-down\n            if (name === \"onPointerDown\") {\n                internal.initialClick = [\n                    event.offsetX,\n                    event.offsetY\n                ];\n                internal.initialHits = hits.map((hit)=>hit.eventObject);\n            }\n            // If a click yields no results, pass it back to the user as a miss\n            // Missed events have to come first in order to establish user-land side-effect clean up\n            if (isClickEvent && !hits.length) {\n                if (delta <= 2) {\n                    pointerMissed(event, internal.interaction);\n                    if (onPointerMissed) onPointerMissed(event);\n                }\n            }\n            // Take care of unhover\n            if (isPointerMove) cancelPointer(hits);\n            function onIntersect(data) {\n                const eventObject = data.eventObject;\n                const instance = eventObject.__r3f;\n                // Check presence of handlers\n                if (!(instance != null && instance.eventCount)) return;\n                const handlers = instance.handlers;\n                /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/ if (isPointerMove) {\n                    // Move event ...\n                    if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n                        // When enter or out is present take care of hover-state\n                        const id = makeId(data);\n                        const hoveredItem = internal.hovered.get(id);\n                        if (!hoveredItem) {\n                            // If the object wasn't previously hovered, book it and call its handler\n                            internal.hovered.set(id, data);\n                            handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n                            handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n                        } else if (hoveredItem.stopped) {\n                            // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n                            data.stopPropagation();\n                        }\n                    }\n                    // Call mouse move\n                    handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n                } else {\n                    // All other events ...\n                    const handler = handlers[name];\n                    if (handler) {\n                        // Forward all events back to their respective handlers with the exception of click events,\n                        // which must use the initial target\n                        if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n                            // Missed events have to come first\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                            // Now call the handler\n                            handler(data);\n                        }\n                    } else {\n                        // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n                        if (isClickEvent && internal.initialHits.includes(eventObject)) {\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                        }\n                    }\n                }\n            }\n            handleIntersects(hits, event, delta, onIntersect);\n        };\n    }\n    return {\n        handlePointer\n    };\n}\nconst isRenderer = (def)=>!!(def != null && def.render);\nconst context = /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance)=>{\n    const rootStore = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get)=>{\n        const position = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        const tempTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n            const { width, height, top, left } = size;\n            const aspect = width / height;\n            if (target.isVector3) tempTarget.copy(target);\n            else tempTarget.set(...target);\n            const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n            if (isOrthographicCamera(camera)) {\n                return {\n                    width: width / camera.zoom,\n                    height: height / camera.zoom,\n                    top,\n                    left,\n                    factor: 1,\n                    distance,\n                    aspect\n                };\n            } else {\n                const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n                const h = 2 * Math.tan(fov / 2) * distance; // visible height\n                const w = h * (width / height);\n                return {\n                    width: w,\n                    height: h,\n                    top,\n                    left,\n                    factor: width / w,\n                    distance,\n                    aspect\n                };\n            }\n        }\n        let performanceTimeout = undefined;\n        const setPerformanceCurrent = (current)=>set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        current\n                    }\n                }));\n        const pointer = new three__WEBPACK_IMPORTED_MODULE_6__.Vector2();\n        const rootState = {\n            set,\n            get,\n            // Mock objects that have to be configured\n            gl: null,\n            camera: null,\n            raycaster: null,\n            events: {\n                priority: 1,\n                enabled: true,\n                connected: false\n            },\n            scene: null,\n            xr: null,\n            invalidate: (frames = 1)=>invalidate(get(), frames),\n            advance: (timestamp, runGlobalEffects)=>advance(timestamp, runGlobalEffects, get()),\n            legacy: false,\n            linear: false,\n            flat: false,\n            controls: null,\n            clock: new three__WEBPACK_IMPORTED_MODULE_6__.Clock(),\n            pointer,\n            mouse: pointer,\n            frameloop: \"always\",\n            onPointerMissed: undefined,\n            performance: {\n                current: 1,\n                min: 0.5,\n                max: 1,\n                debounce: 200,\n                regress: ()=>{\n                    const state = get();\n                    // Clear timeout\n                    if (performanceTimeout) clearTimeout(performanceTimeout);\n                    // Set lower bound performance\n                    if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n                    // Go back to upper bound performance after a while unless something regresses meanwhile\n                    performanceTimeout = setTimeout(()=>setPerformanceCurrent(get().performance.max), state.performance.debounce);\n                }\n            },\n            size: {\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0\n            },\n            viewport: {\n                initialDpr: 0,\n                dpr: 0,\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0,\n                aspect: 0,\n                distance: 0,\n                factor: 0,\n                getCurrentViewport\n            },\n            setEvents: (events)=>set((state)=>({\n                        ...state,\n                        events: {\n                            ...state.events,\n                            ...events\n                        }\n                    })),\n            setSize: (width, height, top = 0, left = 0)=>{\n                const camera = get().camera;\n                const size = {\n                    width,\n                    height,\n                    top,\n                    left\n                };\n                set((state)=>({\n                        size,\n                        viewport: {\n                            ...state.viewport,\n                            ...getCurrentViewport(camera, defaultTarget, size)\n                        }\n                    }));\n            },\n            setDpr: (dpr)=>set((state)=>{\n                    const resolved = calculateDpr(dpr);\n                    return {\n                        viewport: {\n                            ...state.viewport,\n                            dpr: resolved,\n                            initialDpr: state.viewport.initialDpr || resolved\n                        }\n                    };\n                }),\n            setFrameloop: (frameloop = \"always\")=>{\n                const clock = get().clock;\n                // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n                clock.stop();\n                clock.elapsedTime = 0;\n                if (frameloop !== \"never\") {\n                    clock.start();\n                    clock.elapsedTime = 0;\n                }\n                set(()=>({\n                        frameloop\n                    }));\n            },\n            previousRoot: undefined,\n            internal: {\n                // Events\n                interaction: [],\n                hovered: new Map(),\n                subscribers: [],\n                initialClick: [\n                    0,\n                    0\n                ],\n                initialHits: [],\n                capturedMap: new Map(),\n                lastEvent: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n                // Updates\n                active: false,\n                frames: 0,\n                priority: 0,\n                subscribe: (ref, priority, store)=>{\n                    const internal = get().internal;\n                    // If this subscription was given a priority, it takes rendering into its own hands\n                    // For that reason we switch off automatic rendering and increase the manual flag\n                    // As long as this flag is positive there can be no internal rendering at all\n                    // because there could be multiple render subscriptions\n                    internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n                    internal.subscribers.push({\n                        ref,\n                        priority,\n                        store\n                    });\n                    // Register subscriber and sort layers from lowest to highest, meaning,\n                    // highest priority renders last (on top of the other frames)\n                    internal.subscribers = internal.subscribers.sort((a, b)=>a.priority - b.priority);\n                    return ()=>{\n                        const internal = get().internal;\n                        if (internal != null && internal.subscribers) {\n                            // Decrease manual flag if this subscription had a priority\n                            internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n                            // Remove subscriber from list\n                            internal.subscribers = internal.subscribers.filter((s)=>s.ref !== ref);\n                        }\n                    };\n                }\n            }\n        };\n        return rootState;\n    });\n    const state = rootStore.getState();\n    let oldSize = state.size;\n    let oldDpr = state.viewport.dpr;\n    let oldCamera = state.camera;\n    rootStore.subscribe(()=>{\n        const { camera, size, viewport, gl, set } = rootStore.getState();\n        // Resize camera and renderer on changes to size and pixelratio\n        if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n            oldSize = size;\n            oldDpr = viewport.dpr;\n            // Update camera & renderer\n            updateCamera(camera, size);\n            if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n            const updateStyle = typeof HTMLCanvasElement !== \"undefined\" && gl.domElement instanceof HTMLCanvasElement;\n            gl.setSize(size.width, size.height, updateStyle);\n        }\n        // Update viewport once the camera changes\n        if (camera !== oldCamera) {\n            oldCamera = camera;\n            // Update viewport\n            set((state)=>({\n                    viewport: {\n                        ...state.viewport,\n                        ...state.viewport.getCurrentViewport(camera)\n                    }\n                }));\n        }\n    });\n    // Invalidate on any change\n    rootStore.subscribe((state)=>invalidate(state));\n    // Return root state\n    return rootStore;\n};\n/**\r\n * Exposes an object's {@link Instance}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */ function useInstanceHandle(ref) {\n    const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(instance, ()=>ref.current.__r3f, [\n        ref\n    ]);\n    return instance;\n}\n/**\r\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\r\n */ function useStore() {\n    const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n    if (!store) throw new Error(\"R3F: Hooks can only be used within the Canvas component!\");\n    return store;\n}\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */ function useThree(selector = (state)=>state, equalityFn) {\n    return useStore()(selector, equalityFn);\n}\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */ function useFrame(callback, renderPriority = 0) {\n    const store = useStore();\n    const subscribe = store.getState().internal.subscribe;\n    // Memoize ref\n    const ref = useMutableCallback(callback);\n    // Subscribe on mount, unsubscribe on unmount\n    useIsomorphicLayoutEffect(()=>subscribe(ref, renderPriority, store), [\n        renderPriority,\n        subscribe,\n        store\n    ]);\n    return null;\n}\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */ function useGraph(object) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>buildGraph(object), [\n        object\n    ]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = (value)=>{\n    var _value$prototype;\n    return typeof value === \"function\" && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n    return function(Proto, ...input) {\n        let loader;\n        // Construct and cache loader if constructor was passed\n        if (isConstructor$1(Proto)) {\n            loader = memoizedLoaders.get(Proto);\n            if (!loader) {\n                loader = new Proto();\n                memoizedLoaders.set(Proto, loader);\n            }\n        } else {\n            loader = Proto;\n        }\n        // Apply loader extensions\n        if (extensions) extensions(loader);\n        // Go through the urls and load them\n        return Promise.all(input.map((input)=>new Promise((res, reject)=>loader.load(input, (data)=>{\n                    if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n                    res(data);\n                }, onProgress, (error)=>reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n    };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */ function useLoader(loader, input, extensions, onProgress) {\n    // Use suspense to load async assets\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.suspend)(loadingFn(extensions, onProgress), [\n        loader,\n        ...keys\n    ], {\n        equal: is.equ\n    });\n    // Return the object(s)\n    return Array.isArray(input) ? results : results[0];\n}\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */ useLoader.preload = function(loader, input, extensions) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.preload)(loadingFn(extensions), [\n        loader,\n        ...keys\n    ]);\n};\n/**\r\n * Removes a loaded asset from cache.\r\n */ useLoader.clear = function(loader, input) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.clear)([\n        loader,\n        ...keys\n    ]);\n};\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\nfunction createReconciler(config) {\n    const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()(config);\n    reconciler.injectIntoDevTools({\n        bundleType: typeof process !== \"undefined\" && \"development\" !== \"production\" ? 1 : 0,\n        rendererPackageName: \"@react-three/fiber\",\n        version: react__WEBPACK_IMPORTED_MODULE_0__.version\n    });\n    return reconciler;\n}\nconst NoEventPriority = 0;\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = (type)=>`${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = (object)=>typeof object === \"function\";\nfunction extend(objects) {\n    if (isConstructor(objects)) {\n        const Component = `${i++}`;\n        catalogue[Component] = objects;\n        return Component;\n    } else {\n        Object.assign(catalogue, objects);\n    }\n}\nfunction validateInstance(type, props) {\n    // Get target from catalogue\n    const name = toPascalCase(type);\n    const target = catalogue[name];\n    // Validate element target\n    if (type !== \"primitive\" && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n    // Validate primitives\n    if (type === \"primitive\" && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n    // Throw if an object or literal was passed for args\n    if (props.args !== undefined && !Array.isArray(props.args)) throw new Error(\"R3F: The args prop must be an array!\");\n}\nfunction createInstance(type, props, root) {\n    var _props$object;\n    // Remove three* prefix from elements if native element not present\n    type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, \"\");\n    validateInstance(type, props);\n    // Regenerate the R3F instance for primitives to simulate a new object\n    if (type === \"primitive\" && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n    return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n    if (!instance.isHidden) {\n        var _instance$parent;\n        if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n            detach(instance.parent, instance);\n        } else if (isObject3D(instance.object)) {\n            instance.object.visible = false;\n        }\n        instance.isHidden = true;\n        invalidateInstance(instance);\n    }\n}\nfunction unhideInstance(instance) {\n    if (instance.isHidden) {\n        var _instance$parent2;\n        if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n            attach(instance.parent, instance);\n        } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n            instance.object.visible = true;\n        }\n        instance.isHidden = false;\n        invalidateInstance(instance);\n    }\n}\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n    // Bail if tree isn't mounted or parent is not a container.\n    // This ensures that the tree is finalized and React won't discard results to Suspense\n    const state = child.root.getState();\n    if (!parent.parent && parent.object !== state.scene) return;\n    // Create & link object on first run\n    if (!child.object) {\n        var _child$props$object, _child$props$args;\n        // Get target from catalogue\n        const target = catalogue[toPascalCase(child.type)];\n        // Create object\n        child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...(_child$props$args = child.props.args) != null ? _child$props$args : []);\n        child.object.__r3f = child;\n    }\n    // Set initial props\n    applyProps(child.object, child.props);\n    // Append instance\n    if (child.props.attach) {\n        attach(parent, child);\n    } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n        const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n        if (beforeChild && childIndex !== -1) {\n            // If the child is already in the parent's children array, move it to the new position\n            // Otherwise, just insert it at the target position\n            const existingIndex = parent.object.children.indexOf(child.object);\n            if (existingIndex !== -1) {\n                parent.object.children.splice(existingIndex, 1);\n                const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n                parent.object.children.splice(adjustedIndex, 0, child.object);\n            } else {\n                child.object.parent = parent.object;\n                parent.object.children.splice(childIndex, 0, child.object);\n                child.object.dispatchEvent({\n                    type: \"added\"\n                });\n                parent.object.dispatchEvent({\n                    type: \"childadded\",\n                    child: child.object\n                });\n            }\n        } else {\n            parent.object.add(child.object);\n        }\n    }\n    // Link subtree\n    for (const childInstance of child.children)handleContainerEffects(child, childInstance);\n    // Tree was updated, request a frame\n    invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n    if (!child) return;\n    // Link instances\n    child.parent = parent;\n    parent.children.push(child);\n    // Attach tree once complete\n    handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n    if (!child || !beforeChild) return;\n    // Link instances\n    child.parent = parent;\n    const childIndex = parent.children.indexOf(beforeChild);\n    if (childIndex !== -1) parent.children.splice(childIndex, 0, child);\n    else parent.children.push(child);\n    // Attach tree once complete\n    handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n    if (typeof object.dispose === \"function\") {\n        const handleDispose = ()=>{\n            try {\n                object.dispose();\n            } catch  {\n            // no-op\n            }\n        };\n        // In a testing environment, cleanup immediately\n        if (typeof IS_REACT_ACT_ENVIRONMENT !== \"undefined\") handleDispose();\n        else (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, handleDispose);\n    }\n}\nfunction removeChild(parent, child, dispose) {\n    if (!child) return;\n    // Unlink instances\n    child.parent = null;\n    const childIndex = parent.children.indexOf(child);\n    if (childIndex !== -1) parent.children.splice(childIndex, 1);\n    // Eagerly tear down tree\n    if (child.props.attach) {\n        detach(parent, child);\n    } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n        parent.object.remove(child.object);\n        removeInteractivity(findInitialRoot(child), child.object);\n    }\n    // Allow objects to bail out of unmount disposal with dispose={null}\n    const shouldDispose = child.props.dispose !== null && dispose !== false;\n    // Recursively remove instance children\n    for(let i = child.children.length - 1; i >= 0; i--){\n        const node = child.children[i];\n        removeChild(child, node, shouldDispose);\n    }\n    child.children.length = 0;\n    // Unlink instance object\n    delete child.object.__r3f;\n    // Dispose object whenever the reconciler feels like it.\n    // Never dispose of primitives because their state may be kept outside of React!\n    // In order for an object to be able to dispose it\n    //   - has a dispose method\n    //   - cannot be a <primitive object={...} />\n    //   - cannot be a THREE.Scene, because three has broken its own API\n    if (shouldDispose && child.type !== \"primitive\" && child.object.type !== \"Scene\") {\n        disposeOnIdle(child.object);\n    }\n    // Tree was updated, request a frame for top-level instance\n    if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n    for (const _fiber of [\n        fiber,\n        fiber.alternate\n    ]){\n        if (_fiber !== null) {\n            if (typeof _fiber.ref === \"function\") {\n                _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n                const cleanup = _fiber.ref(publicInstance);\n                if (typeof cleanup === \"function\") _fiber.refCleanup = cleanup;\n            } else if (_fiber.ref) {\n                _fiber.ref.current = publicInstance;\n            }\n        }\n    }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n    // Detach instance\n    for (const [instance] of reconstructed){\n        const parent = instance.parent;\n        if (parent) {\n            if (instance.props.attach) {\n                detach(parent, instance);\n            } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n                parent.object.remove(instance.object);\n            }\n            for (const child of instance.children){\n                if (child.props.attach) {\n                    detach(instance, child);\n                } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n                    instance.object.remove(child.object);\n                }\n            }\n        }\n        // If the old instance is hidden, we need to unhide it.\n        // React assumes it can discard instances since they're pure for DOM.\n        // This isn't true for us since our lifetimes are impure and longliving.\n        // So, we manually check if an instance was hidden and unhide it.\n        if (instance.isHidden) unhideInstance(instance);\n        // Dispose of old object if able\n        if (instance.object.__r3f) delete instance.object.__r3f;\n        if (instance.type !== \"primitive\") disposeOnIdle(instance.object);\n    }\n    // Update instance\n    for (const [instance, props, fiber] of reconstructed){\n        instance.props = props;\n        const parent = instance.parent;\n        if (parent) {\n            var _instance$props$objec, _instance$props$args;\n            // Get target from catalogue\n            const target = catalogue[toPascalCase(instance.type)];\n            // Create object\n            instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...(_instance$props$args = instance.props.args) != null ? _instance$props$args : []);\n            instance.object.__r3f = instance;\n            setFiberRef(fiber, instance.object);\n            // Set initial props\n            applyProps(instance.object, instance.props);\n            if (instance.props.attach) {\n                attach(parent, instance);\n            } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n                parent.object.add(instance.object);\n            }\n            for (const child of instance.children){\n                if (child.props.attach) {\n                    attach(instance, child);\n                } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n                    instance.object.add(child.object);\n                }\n            }\n            // Tree was updated, request a frame\n            invalidateInstance(instance);\n        }\n    }\n    reconstructed.length = 0;\n}\n// Don't handle text instances, make it no-op\nconst handleTextInstance = ()=>{};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */ createReconciler({\n    isPrimaryRenderer: false,\n    warnsIfNotActing: false,\n    supportsMutation: true,\n    supportsPersistence: false,\n    supportsHydration: false,\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    appendChildToContainer (container, child) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !scene) return;\n        appendChild(scene, child);\n    },\n    removeChildFromContainer (container, child) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !scene) return;\n        removeChild(scene, child);\n    },\n    insertInContainerBefore (container, child, beforeChild) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !beforeChild || !scene) return;\n        insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: ()=>NO_CONTEXT,\n    getChildHostContext: ()=>NO_CONTEXT,\n    commitUpdate (instance, type, oldProps, newProps, fiber) {\n        var _newProps$args, _oldProps$args, _newProps$args2;\n        validateInstance(type, newProps);\n        let reconstruct = false;\n        // Reconstruct primitives if object prop changes\n        if (instance.type === \"primitive\" && oldProps.object !== newProps.object) reconstruct = true;\n        else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n        else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index)=>{\n            var _oldProps$args2;\n            return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n        })) reconstruct = true;\n        // Reconstruct when args or <primitive object={...} have changes\n        if (reconstruct) {\n            reconstructed.push([\n                instance,\n                {\n                    ...newProps\n                },\n                fiber\n            ]);\n        } else {\n            // Create a diff-set, flag if there are any changes\n            const changedProps = diffProps(instance, newProps);\n            if (Object.keys(changedProps).length) {\n                Object.assign(instance.props, changedProps);\n                applyProps(instance.object, changedProps);\n            }\n        }\n        // Flush reconstructed siblings when we hit the last updated child in a sequence\n        const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n        if (isTailSibling) swapInstances();\n    },\n    finalizeInitialChildren: ()=>false,\n    commitMount () {},\n    getPublicInstance: (instance)=>instance == null ? void 0 : instance.object,\n    prepareForCommit: ()=>null,\n    preparePortalMount: (container)=>prepare(container.getState().scene, container, \"\", {}),\n    resetAfterCommit: ()=>{},\n    shouldSetTextContent: ()=>false,\n    clearContainer: ()=>false,\n    hideInstance,\n    unhideInstance,\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    scheduleTimeout: typeof setTimeout === \"function\" ? setTimeout : undefined,\n    cancelTimeout: typeof clearTimeout === \"function\" ? clearTimeout : undefined,\n    noTimeout: -1,\n    getInstanceFromNode: ()=>null,\n    beforeActiveInstanceBlur () {},\n    afterActiveInstanceBlur () {},\n    detachDeletedInstance () {},\n    prepareScopeUpdate () {},\n    getInstanceFromScope: ()=>null,\n    shouldAttemptEagerTransition: ()=>false,\n    trackSchedulerEvent: ()=>{},\n    resolveEventType: ()=>null,\n    resolveEventTimeStamp: ()=>-1.1,\n    requestPostPaintCallback () {},\n    maySuspendCommit: ()=>false,\n    preloadInstance: ()=>true,\n    // true indicates already loaded\n    startSuspendingCommit () {},\n    suspendInstance () {},\n    waitForCommitToBeReady: ()=>null,\n    NotPendingTransition: null,\n    HostTransitionContext: /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null),\n    setCurrentUpdatePriority (newPriority) {\n        currentUpdatePriority = newPriority;\n    },\n    getCurrentUpdatePriority () {\n        return currentUpdatePriority;\n    },\n    resolveUpdatePriority () {\n        var _window$event;\n        if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n        switch( false && (0)){\n            case \"click\":\n            case \"contextmenu\":\n            case \"dblclick\":\n            case \"pointercancel\":\n            case \"pointerdown\":\n            case \"pointerup\":\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n            case \"pointermove\":\n            case \"pointerout\":\n            case \"pointerover\":\n            case \"pointerenter\":\n            case \"pointerleave\":\n            case \"wheel\":\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n            default:\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n        }\n    },\n    resetFormInstance () {}\n});\nconst _roots = new Map();\nconst shallowLoose = {\n    objects: \"shallow\",\n    strict: false\n};\nfunction computeInitialSize(canvas, size) {\n    if (!size && typeof HTMLCanvasElement !== \"undefined\" && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n        const { width, height, top, left } = canvas.parentElement.getBoundingClientRect();\n        return {\n            width,\n            height,\n            top,\n            left\n        };\n    } else if (!size && typeof OffscreenCanvas !== \"undefined\" && canvas instanceof OffscreenCanvas) {\n        return {\n            width: canvas.width,\n            height: canvas.height,\n            top: 0,\n            left: 0\n        };\n    }\n    return {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        ...size\n    };\n}\nfunction createRoot(canvas) {\n    // Check against mistaken use of createRoot\n    const prevRoot = _roots.get(canvas);\n    const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n    const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n    if (prevRoot) console.warn(\"R3F.createRoot should only be called once!\");\n    // Report when an error was detected in a previous render\n    // https://github.com/pmndrs/react-three-fiber/pull/2261\n    const logRecoverableError = typeof reportError === \"function\" ? // In modern browsers, reportError will dispatch an error event,\n    // emulating an uncaught JavaScript error.\n    reportError : // In older browsers and test environments, fallback to console.error.\n    console.error;\n    // Create store\n    const store = prevStore || createStore(invalidate, advance);\n    // Create renderer\n    const fiber = prevFiber || reconciler.createContainer(store, // container\n    react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, // tag\n    null, // hydration callbacks\n    false, // isStrictMode\n    null, // concurrentUpdatesByDefaultOverride\n    \"\", // identifierPrefix\n    logRecoverableError, // onUncaughtError\n    logRecoverableError, // onCaughtError\n    logRecoverableError, // onRecoverableError\n    null // transitionCallbacks\n    );\n    // Map it\n    if (!prevRoot) _roots.set(canvas, {\n        fiber,\n        store\n    });\n    // Locals\n    let onCreated;\n    let lastCamera;\n    let configured = false;\n    let pending = null;\n    return {\n        async configure (props = {}) {\n            let resolve;\n            pending = new Promise((_resolve)=>resolve = _resolve);\n            let { gl: glConfig, size: propsSize, scene: sceneOptions, events, onCreated: onCreatedCallback, shadows = false, linear = false, flat = false, legacy = false, orthographic = false, frameloop = \"always\", dpr = [\n                1,\n                2\n            ], performance, raycaster: raycastOptions, camera: cameraOptions, onPointerMissed } = props;\n            let state = store.getState();\n            // Set up renderer (one time only!)\n            let gl = state.gl;\n            if (!state.gl) {\n                const defaultProps = {\n                    canvas: canvas,\n                    powerPreference: \"high-performance\",\n                    antialias: true,\n                    alpha: true\n                };\n                const customRenderer = typeof glConfig === \"function\" ? await glConfig(defaultProps) : glConfig;\n                if (isRenderer(customRenderer)) {\n                    gl = customRenderer;\n                } else {\n                    gl = new three__WEBPACK_IMPORTED_MODULE_9__.WebGLRenderer({\n                        ...defaultProps,\n                        ...glConfig\n                    });\n                }\n                state.set({\n                    gl\n                });\n            }\n            // Set up raycaster (one time only!)\n            let raycaster = state.raycaster;\n            if (!raycaster) state.set({\n                raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster()\n            });\n            // Set raycaster options\n            const { params, ...options } = raycastOptions || {};\n            if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n                ...options\n            });\n            if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n                params: {\n                    ...raycaster.params,\n                    ...params\n                }\n            });\n            // Create default camera, don't overwrite any user-set state\n            if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n                lastCamera = cameraOptions;\n                const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n                const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_6__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_6__.PerspectiveCamera(75, 0, 0.1, 1000);\n                if (!isCamera) {\n                    camera.position.z = 5;\n                    if (cameraOptions) {\n                        applyProps(camera, cameraOptions);\n                        // Preserve user-defined frustum if possible\n                        // https://github.com/pmndrs/react-three-fiber/issues/3160\n                        if (!camera.manual) {\n                            if (\"aspect\" in cameraOptions || \"left\" in cameraOptions || \"right\" in cameraOptions || \"bottom\" in cameraOptions || \"top\" in cameraOptions) {\n                                camera.manual = true;\n                                camera.updateProjectionMatrix();\n                            }\n                        }\n                    }\n                    // Always look at center by default\n                    if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n                }\n                state.set({\n                    camera\n                });\n                // Configure raycaster\n                // https://github.com/pmndrs/react-xr/issues/300\n                raycaster.camera = camera;\n            }\n            // Set up scene (one time only!)\n            if (!state.scene) {\n                let scene;\n                if (sceneOptions != null && sceneOptions.isScene) {\n                    scene = sceneOptions;\n                    prepare(scene, store, \"\", {});\n                } else {\n                    scene = new three__WEBPACK_IMPORTED_MODULE_6__.Scene();\n                    prepare(scene, store, \"\", {});\n                    if (sceneOptions) applyProps(scene, sceneOptions);\n                }\n                state.set({\n                    scene\n                });\n            }\n            // Store events internally\n            if (events && !state.events.handlers) state.set({\n                events: events(store)\n            });\n            // Check size, allow it to take on container bounds initially\n            const size = computeInitialSize(canvas, propsSize);\n            if (!is.equ(size, state.size, shallowLoose)) {\n                state.setSize(size.width, size.height, size.top, size.left);\n            }\n            // Check pixelratio\n            if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n            // Check frameloop\n            if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n            // Check pointer missed\n            if (!state.onPointerMissed) state.set({\n                onPointerMissed\n            });\n            // Check performance\n            if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        ...performance\n                    }\n                }));\n            // Set up XR (one time only!)\n            if (!state.xr) {\n                var _gl$xr;\n                // Handle frame behavior in WebXR\n                const handleXRFrame = (timestamp, frame)=>{\n                    const state = store.getState();\n                    if (state.frameloop === \"never\") return;\n                    advance(timestamp, true, state, frame);\n                };\n                // Toggle render switching on session\n                const handleSessionChange = ()=>{\n                    const state = store.getState();\n                    state.gl.xr.enabled = state.gl.xr.isPresenting;\n                    state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n                    if (!state.gl.xr.isPresenting) invalidate(state);\n                };\n                // WebXR session manager\n                const xr = {\n                    connect () {\n                        const gl = store.getState().gl;\n                        gl.xr.addEventListener(\"sessionstart\", handleSessionChange);\n                        gl.xr.addEventListener(\"sessionend\", handleSessionChange);\n                    },\n                    disconnect () {\n                        const gl = store.getState().gl;\n                        gl.xr.removeEventListener(\"sessionstart\", handleSessionChange);\n                        gl.xr.removeEventListener(\"sessionend\", handleSessionChange);\n                    }\n                };\n                // Subscribe to WebXR session events\n                if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === \"function\") xr.connect();\n                state.set({\n                    xr\n                });\n            }\n            // Set shadowmap\n            if (gl.shadowMap) {\n                const oldEnabled = gl.shadowMap.enabled;\n                const oldType = gl.shadowMap.type;\n                gl.shadowMap.enabled = !!shadows;\n                if (is.boo(shadows)) {\n                    gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n                } else if (is.str(shadows)) {\n                    var _types$shadows;\n                    const types = {\n                        basic: three__WEBPACK_IMPORTED_MODULE_6__.BasicShadowMap,\n                        percentage: three__WEBPACK_IMPORTED_MODULE_6__.PCFShadowMap,\n                        soft: three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap,\n                        variance: three__WEBPACK_IMPORTED_MODULE_6__.VSMShadowMap\n                    };\n                    gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n                } else if (is.obj(shadows)) {\n                    Object.assign(gl.shadowMap, shadows);\n                }\n                if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n            }\n            three__WEBPACK_IMPORTED_MODULE_6__.ColorManagement.enabled = !legacy;\n            // Set color space and tonemapping preferences\n            if (!configured) {\n                gl.outputColorSpace = linear ? three__WEBPACK_IMPORTED_MODULE_6__.LinearSRGBColorSpace : three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n                gl.toneMapping = flat ? three__WEBPACK_IMPORTED_MODULE_6__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_6__.ACESFilmicToneMapping;\n            }\n            // Update color management state\n            if (state.legacy !== legacy) state.set(()=>({\n                    legacy\n                }));\n            if (state.linear !== linear) state.set(()=>({\n                    linear\n                }));\n            if (state.flat !== flat) state.set(()=>({\n                    flat\n                }));\n            // Set gl props\n            if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n            // Set locals\n            onCreated = onCreatedCallback;\n            configured = true;\n            resolve();\n            return this;\n        },\n        render (children) {\n            // The root has to be configured before it can be rendered\n            if (!configured && !pending) this.configure();\n            pending.then(()=>{\n                reconciler.updateContainer(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Provider, {\n                    store: store,\n                    children: children,\n                    onCreated: onCreated,\n                    rootElement: canvas\n                }), fiber, null, ()=>undefined);\n            });\n            return store;\n        },\n        unmount () {\n            unmountComponentAtNode(canvas);\n        }\n    };\n}\nfunction Provider({ store, children, onCreated, rootElement }) {\n    useIsomorphicLayoutEffect(()=>{\n        const state = store.getState();\n        // Flag the canvas active, rendering will now begin\n        state.set((state)=>({\n                internal: {\n                    ...state.internal,\n                    active: true\n                }\n            }));\n        // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n        if (onCreated) onCreated(state);\n        // Connect events to the targets parent, this is done to ensure events are registered on\n        // a shared target, and not on the canvas itself\n        if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n        value: store,\n        children: children\n    });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n    const root = _roots.get(canvas);\n    const fiber = root == null ? void 0 : root.fiber;\n    if (fiber) {\n        const state = root == null ? void 0 : root.store.getState();\n        if (state) state.internal.active = false;\n        reconciler.updateContainer(null, fiber, null, ()=>{\n            if (state) {\n                setTimeout(()=>{\n                    try {\n                        var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n                        state.events.disconnect == null ? void 0 : state.events.disconnect();\n                        (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n                        (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n                        if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n                        dispose(state.scene);\n                        _roots.delete(canvas);\n                        if (callback) callback(canvas);\n                    } catch (e) {\n                    /* ... */ }\n                }, 500);\n            }\n        });\n    }\n}\nfunction createPortal(children, container, state) {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Portal, {\n        children: children,\n        container: container,\n        state: state\n    });\n}\nfunction Portal({ state = {}, children, container }) {\n    /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */ const { events, size, ...rest } = state;\n    const previousRoot = useStore();\n    const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster());\n    const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new three__WEBPACK_IMPORTED_MODULE_6__.Vector2());\n    const inject = useMutableCallback((rootState, injectState)=>{\n        let viewport = undefined;\n        if (injectState.camera && size) {\n            const camera = injectState.camera;\n            // Calculate the override viewport, if present\n            viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(), size);\n            // Update the portal camera, if it differs from the previous layer\n            if (camera !== rootState.camera) updateCamera(camera, size);\n        }\n        return {\n            // The intersect consists of the previous root state\n            ...rootState,\n            ...injectState,\n            // Portals have their own scene, which forms the root, a raycaster and a pointer\n            scene: container,\n            raycaster,\n            pointer,\n            mouse: pointer,\n            // Their previous root is the layer before it\n            previousRoot,\n            // Events, size and viewport can be overridden by the inject layer\n            events: {\n                ...rootState.events,\n                ...injectState.events,\n                ...events\n            },\n            size: {\n                ...rootState.size,\n                ...size\n            },\n            viewport: {\n                ...rootState.viewport,\n                ...viewport\n            },\n            // Layers are allowed to override events\n            setEvents: (events)=>injectState.set((state)=>({\n                        ...state,\n                        events: {\n                            ...state.events,\n                            ...events\n                        }\n                    }))\n        };\n    });\n    const usePortalStore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        // Create a mirrored store, based on the previous root with a few overrides ...\n        const store = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get)=>({\n                ...rest,\n                set,\n                get\n            }));\n        // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n        const onMutate = (prev)=>store.setState((state)=>inject.current(prev, state));\n        onMutate(previousRoot.getState());\n        previousRoot.subscribe(onMutate);\n        return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        previousRoot,\n        container\n    ]);\n    return(/*#__PURE__*/ // @ts-ignore, reconciler types are not maintained\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n        children: reconciler.createPortal(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n            value: usePortalStore,\n            children: children\n        }), usePortalStore, null)\n    }));\n}\nfunction createSubs(callback, subs) {\n    const sub = {\n        callback\n    };\n    subs.add(sub);\n    return ()=>void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */ const addEffect = (callback)=>createSubs(callback, globalEffects);\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */ const addAfterEffect = (callback)=>createSubs(callback, globalAfterEffects);\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */ const addTail = (callback)=>createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n    if (!effects.size) return;\n    for (const { callback } of effects.values()){\n        callback(timestamp);\n    }\n}\nfunction flushGlobalEffects(type, timestamp) {\n    switch(type){\n        case \"before\":\n            return run(globalEffects, timestamp);\n        case \"after\":\n            return run(globalAfterEffects, timestamp);\n        case \"tail\":\n            return run(globalTailEffects, timestamp);\n    }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n    // Run local effects\n    let delta = state.clock.getDelta();\n    // In frameloop='never' mode, clock times are updated using the provided timestamp\n    if (state.frameloop === \"never\" && typeof timestamp === \"number\") {\n        delta = timestamp - state.clock.elapsedTime;\n        state.clock.oldTime = state.clock.elapsedTime;\n        state.clock.elapsedTime = timestamp;\n    }\n    // Call subscribers (useFrame)\n    subscribers = state.internal.subscribers;\n    for(let i = 0; i < subscribers.length; i++){\n        subscription = subscribers[i];\n        subscription.ref.current(subscription.store.getState(), delta, frame);\n    }\n    // Render content\n    if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n    // Decrease frame count\n    state.internal.frames = Math.max(0, state.internal.frames - 1);\n    return state.frameloop === \"always\" ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n    // Run effects\n    flushGlobalEffects(\"before\", timestamp);\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of _roots.values()){\n        var _state$gl$xr;\n        state = root.store.getState();\n        // If the frameloop is invalidated, do not run another frame\n        if (state.internal.active && (state.frameloop === \"always\" || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n            repeat += update(timestamp, state);\n        }\n    }\n    useFrameInProgress = false;\n    // Run after-effects\n    flushGlobalEffects(\"after\", timestamp);\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n        // Tail call effects, they are called when rendering stops\n        flushGlobalEffects(\"tail\", timestamp);\n        // Flag end of operation\n        running = false;\n        return cancelAnimationFrame(frame);\n    }\n}\n/**\r\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n */ function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return _roots.forEach((root)=>invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === \"never\") return;\n    if (frames > 1) {\n        // legacy support for people using frames parameters\n        // Increase frames, do not go higher than 60\n        state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n        if (useFrameInProgress) {\n            //called from within a useFrame, it means the user wants an additional frame\n            state.internal.frames = 2;\n        } else {\n            //the user need a new frame, no need to increment further than 1\n            state.internal.frames = 1;\n        }\n    }\n    // If the render-loop isn't active, start it\n    if (!running) {\n        running = true;\n        requestAnimationFrame(loop);\n    }\n}\n/**\r\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n */ function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects(\"before\", timestamp);\n    if (!state) for (const root of _roots.values())update(timestamp, root.store.getState());\n    else update(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects(\"after\", timestamp);\n}\nconst DOM_EVENTS = {\n    onClick: [\n        \"click\",\n        false\n    ],\n    onContextMenu: [\n        \"contextmenu\",\n        false\n    ],\n    onDoubleClick: [\n        \"dblclick\",\n        false\n    ],\n    onWheel: [\n        \"wheel\",\n        true\n    ],\n    onPointerDown: [\n        \"pointerdown\",\n        true\n    ],\n    onPointerUp: [\n        \"pointerup\",\n        true\n    ],\n    onPointerLeave: [\n        \"pointerleave\",\n        true\n    ],\n    onPointerMove: [\n        \"pointermove\",\n        true\n    ],\n    onPointerCancel: [\n        \"pointercancel\",\n        true\n    ],\n    onLostPointerCapture: [\n        \"lostpointercapture\",\n        true\n    ]\n};\n/** Default R3F event manager for web */ function createPointerEvents(store) {\n    const { handlePointer } = createEvents(store);\n    return {\n        priority: 1,\n        enabled: true,\n        compute (event, state, previous) {\n            // https://github.com/pmndrs/react-three-fiber/pull/782\n            // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n            state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n            state.raycaster.setFromCamera(state.pointer, state.camera);\n        },\n        connected: undefined,\n        handlers: Object.keys(DOM_EVENTS).reduce((acc, key)=>({\n                ...acc,\n                [key]: handlePointer(key)\n            }), {}),\n        update: ()=>{\n            var _internal$lastEvent;\n            const { events, internal } = store.getState();\n            if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n        },\n        connect: (target)=>{\n            const { set, events } = store.getState();\n            events.disconnect == null ? void 0 : events.disconnect();\n            set((state)=>({\n                    events: {\n                        ...state.events,\n                        connected: target\n                    }\n                }));\n            if (events.handlers) {\n                for(const name in events.handlers){\n                    const event = events.handlers[name];\n                    const [eventName, passive] = DOM_EVENTS[name];\n                    target.addEventListener(eventName, event, {\n                        passive\n                    });\n                }\n            }\n        },\n        disconnect: ()=>{\n            const { set, events } = store.getState();\n            if (events.connected) {\n                if (events.handlers) {\n                    for(const name in events.handlers){\n                        const event = events.handlers[name];\n                        const [eventName] = DOM_EVENTS[name];\n                        events.connected.removeEventListener(eventName, event);\n                    }\n                }\n                set((state)=>({\n                        events: {\n                            ...state.events,\n                            connected: undefined\n                        }\n                    }));\n            }\n        }\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__._),\n/* harmony export */   act: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   advance: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   context: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   events: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   extend: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)\n/* harmony export */ });\n/* harmony import */ var _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-dc44c1b8.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CanvasImpl({ ref, children, fallback, resize, style, gl, events = _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f, eventSource, eventPrefix, shadows, linear, flat, legacy, orthographic, frameloop, dpr, performance, raycaster, camera, scene, onPointerMissed, onCreated, ...props }) {\n    // Create a known catalogue of Threejs-native elements\n    // This will include the entire THREE namespace by default, users can extend\n    // their own elements by using the createRoot API instead\n    react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>(0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n    const Bridge = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)();\n    const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        scroll: true,\n        debounce: {\n            scroll: 50,\n            resize: 0\n        },\n        ...resize\n    });\n    const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, ()=>canvasRef.current);\n    const handlePointerMissed = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(onPointerMissed);\n    const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Suspend this component if block is a promise (2nd run)\n    if (block) throw block;\n    // Throw exception outwards if anything within canvas throws\n    if (error) throw error;\n    const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(()=>{\n        const canvas = canvasRef.current;\n        if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n            if (!root.current) root.current = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(canvas);\n            async function run() {\n                await root.current.configure({\n                    gl,\n                    scene,\n                    events,\n                    shadows,\n                    linear,\n                    flat,\n                    legacy,\n                    orthographic,\n                    frameloop,\n                    dpr,\n                    performance,\n                    raycaster,\n                    camera,\n                    size: containerRect,\n                    // Pass mutable reference to onPointerMissed so it's free to update\n                    onPointerMissed: (...args)=>handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n                    onCreated: (state)=>{\n                        // Connect to event source\n                        state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n                        // Set up compute function\n                        if (eventPrefix) {\n                            state.setEvents({\n                                compute: (event, state)=>{\n                                    const x = event[eventPrefix + \"X\"];\n                                    const y = event[eventPrefix + \"Y\"];\n                                    state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                                    state.raycaster.setFromCamera(state.pointer, state.camera);\n                                }\n                            });\n                        }\n                        // Call onCreated callback\n                        onCreated == null ? void 0 : onCreated(state);\n                    }\n                });\n                root.current.render(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n                        set: setError,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                            fallback: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n                                set: setBlock\n                            }),\n                            children: children != null ? children : null\n                        })\n                    })\n                }));\n            }\n            run();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const canvas = canvasRef.current;\n        if (canvas) return ()=>(0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n    }, []);\n    // When the event source is not this div, we need to set pointer-events to none\n    // Or else the canvas will block events from reaching the event source\n    const pointerEvents = eventSource ? \"none\" : \"auto\";\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        ref: divRef,\n        style: {\n            position: \"relative\",\n            width: \"100%\",\n            height: \"100%\",\n            overflow: \"hidden\",\n            pointerEvents,\n            ...style\n        },\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: containerRef,\n            style: {\n                width: \"100%\",\n                height: \"100%\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: \"block\"\n                },\n                children: fallback\n            })\n        })\n    });\n}\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */ function Canvas(props) {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_8__.FiberProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n            ...props\n        })\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \n true && function() {\n    function performWorkUntilDeadline() {\n        if (isMessageLoopRunning) {\n            var currentTime = exports.unstable_now();\n            startTime = currentTime;\n            var hasMoreWork = !0;\n            try {\n                a: {\n                    isHostCallbackScheduled = !1;\n                    isHostTimeoutScheduled && (isHostTimeoutScheduled = !1, localClearTimeout(taskTimeoutID), taskTimeoutID = -1);\n                    isPerformingWork = !0;\n                    var previousPriorityLevel = currentPriorityLevel;\n                    try {\n                        b: {\n                            advanceTimers(currentTime);\n                            for(currentTask = peek(taskQueue); null !== currentTask && !(currentTask.expirationTime > currentTime && shouldYieldToHost());){\n                                var callback = currentTask.callback;\n                                if (\"function\" === typeof callback) {\n                                    currentTask.callback = null;\n                                    currentPriorityLevel = currentTask.priorityLevel;\n                                    var continuationCallback = callback(currentTask.expirationTime <= currentTime);\n                                    currentTime = exports.unstable_now();\n                                    if (\"function\" === typeof continuationCallback) {\n                                        currentTask.callback = continuationCallback;\n                                        advanceTimers(currentTime);\n                                        hasMoreWork = !0;\n                                        break b;\n                                    }\n                                    currentTask === peek(taskQueue) && pop(taskQueue);\n                                    advanceTimers(currentTime);\n                                } else pop(taskQueue);\n                                currentTask = peek(taskQueue);\n                            }\n                            if (null !== currentTask) hasMoreWork = !0;\n                            else {\n                                var firstTimer = peek(timerQueue);\n                                null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n                                hasMoreWork = !1;\n                            }\n                        }\n                        break a;\n                    } finally{\n                        currentTask = null, currentPriorityLevel = previousPriorityLevel, isPerformingWork = !1;\n                    }\n                    hasMoreWork = void 0;\n                }\n            } finally{\n                hasMoreWork ? schedulePerformWorkUntilDeadline() : isMessageLoopRunning = !1;\n            }\n        }\n    }\n    function push(heap, node) {\n        var index = heap.length;\n        heap.push(node);\n        a: for(; 0 < index;){\n            var parentIndex = index - 1 >>> 1, parent = heap[parentIndex];\n            if (0 < compare(parent, node)) heap[parentIndex] = node, heap[index] = parent, index = parentIndex;\n            else break a;\n        }\n    }\n    function peek(heap) {\n        return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n        if (0 === heap.length) return null;\n        var first = heap[0], last = heap.pop();\n        if (last !== first) {\n            heap[0] = last;\n            a: for(var index = 0, length = heap.length, halfLength = length >>> 1; index < halfLength;){\n                var leftIndex = 2 * (index + 1) - 1, left = heap[leftIndex], rightIndex = leftIndex + 1, right = heap[rightIndex];\n                if (0 > compare(left, last)) rightIndex < length && 0 > compare(right, left) ? (heap[index] = right, heap[rightIndex] = last, index = rightIndex) : (heap[index] = left, heap[leftIndex] = last, index = leftIndex);\n                else if (rightIndex < length && 0 > compare(right, last)) heap[index] = right, heap[rightIndex] = last, index = rightIndex;\n                else break a;\n            }\n        }\n        return first;\n    }\n    function compare(a, b) {\n        var diff = a.sortIndex - b.sortIndex;\n        return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n        for(var timer = peek(timerQueue); null !== timer;){\n            if (null === timer.callback) pop(timerQueue);\n            else if (timer.startTime <= currentTime) pop(timerQueue), timer.sortIndex = timer.expirationTime, push(taskQueue, timer);\n            else break;\n            timer = peek(timerQueue);\n        }\n    }\n    function handleTimeout(currentTime) {\n        isHostTimeoutScheduled = !1;\n        advanceTimers(currentTime);\n        if (!isHostCallbackScheduled) if (null !== peek(taskQueue)) isHostCallbackScheduled = !0, requestHostCallback();\n        else {\n            var firstTimer = peek(timerQueue);\n            null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n        }\n    }\n    function shouldYieldToHost() {\n        return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n        isMessageLoopRunning || (isMessageLoopRunning = !0, schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n        taskTimeoutID = localSetTimeout(function() {\n            callback(exports.unstable_now());\n        }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\"object\" === typeof performance && \"function\" === typeof performance.now) {\n        var localPerformance = performance;\n        exports.unstable_now = function() {\n            return localPerformance.now();\n        };\n    } else {\n        var localDate = Date, initialTime = localDate.now();\n        exports.unstable_now = function() {\n            return localDate.now() - initialTime;\n        };\n    }\n    var taskQueue = [], timerQueue = [], taskIdCounter = 1, currentTask = null, currentPriorityLevel = 3, isPerformingWork = !1, isHostCallbackScheduled = !1, isHostTimeoutScheduled = !1, localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null, localClearTimeout = \"function\" === typeof clearTimeout ? clearTimeout : null, localSetImmediate = \"undefined\" !== typeof setImmediate ? setImmediate : null, isMessageLoopRunning = !1, taskTimeoutID = -1, frameInterval = 5, startTime = -1;\n    if (\"function\" === typeof localSetImmediate) var schedulePerformWorkUntilDeadline = function() {\n        localSetImmediate(performWorkUntilDeadline);\n    };\n    else if (\"undefined\" !== typeof MessageChannel) {\n        var channel = new MessageChannel(), port = channel.port2;\n        channel.port1.onmessage = performWorkUntilDeadline;\n        schedulePerformWorkUntilDeadline = function() {\n            port.postMessage(null);\n        };\n    } else schedulePerformWorkUntilDeadline = function() {\n        localSetTimeout(performWorkUntilDeadline, 0);\n    };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function(task) {\n        task.callback = null;\n    };\n    exports.unstable_continueExecution = function() {\n        isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function(fps) {\n        0 > fps || 125 < fps ? console.error(\"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\") : frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5;\n    };\n    exports.unstable_getCurrentPriorityLevel = function() {\n        return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function() {\n        return peek(taskQueue);\n    };\n    exports.unstable_next = function(eventHandler) {\n        switch(currentPriorityLevel){\n            case 1:\n            case 2:\n            case 3:\n                var priorityLevel = 3;\n                break;\n            default:\n                priorityLevel = currentPriorityLevel;\n        }\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = priorityLevel;\n        try {\n            return eventHandler();\n        } finally{\n            currentPriorityLevel = previousPriorityLevel;\n        }\n    };\n    exports.unstable_pauseExecution = function() {};\n    exports.unstable_requestPaint = function() {};\n    exports.unstable_runWithPriority = function(priorityLevel, eventHandler) {\n        switch(priorityLevel){\n            case 1:\n            case 2:\n            case 3:\n            case 4:\n            case 5:\n                break;\n            default:\n                priorityLevel = 3;\n        }\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = priorityLevel;\n        try {\n            return eventHandler();\n        } finally{\n            currentPriorityLevel = previousPriorityLevel;\n        }\n    };\n    exports.unstable_scheduleCallback = function(priorityLevel, callback, options) {\n        var currentTime = exports.unstable_now();\n        \"object\" === typeof options && null !== options ? (options = options.delay, options = \"number\" === typeof options && 0 < options ? currentTime + options : currentTime) : options = currentTime;\n        switch(priorityLevel){\n            case 1:\n                var timeout = -1;\n                break;\n            case 2:\n                timeout = 250;\n                break;\n            case 5:\n                timeout = 1073741823;\n                break;\n            case 4:\n                timeout = 1e4;\n                break;\n            default:\n                timeout = 5e3;\n        }\n        timeout = options + timeout;\n        priorityLevel = {\n            id: taskIdCounter++,\n            callback: callback,\n            priorityLevel: priorityLevel,\n            startTime: options,\n            expirationTime: timeout,\n            sortIndex: -1\n        };\n        options > currentTime ? (priorityLevel.sortIndex = options, push(timerQueue, priorityLevel), null === peek(taskQueue) && priorityLevel === peek(timerQueue) && (isHostTimeoutScheduled ? (localClearTimeout(taskTimeoutID), taskTimeoutID = -1) : isHostTimeoutScheduled = !0, requestHostTimeout(handleTimeout, options - currentTime))) : (priorityLevel.sortIndex = timeout, push(taskQueue, priorityLevel), isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback()));\n        return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function(callback) {\n        var parentPriorityLevel = currentPriorityLevel;\n        return function() {\n            var previousPriorityLevel = currentPriorityLevel;\n            currentPriorityLevel = parentPriorityLevel;\n            try {\n                return callback.apply(this, arguments);\n            } finally{\n                currentPriorityLevel = previousPriorityLevel;\n            }\n        };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvY2pzL3NjaGVkdWxlci5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7Q0FRQyxHQUVZO0FBQ2IsS0FYQSxJQVlFO0lBQ0UsU0FBU0E7UUFDUCxJQUFJQyxzQkFBc0I7WUFDeEIsSUFBSUMsY0FBY0MsUUFBUUMsWUFBWTtZQUN0Q0MsWUFBWUg7WUFDWixJQUFJSSxjQUFjLENBQUM7WUFDbkIsSUFBSTtnQkFDRkMsR0FBRztvQkFDREMsMEJBQTBCLENBQUM7b0JBQzNCQywwQkFDRywwQkFBMEIsQ0FBQyxHQUM1QkMsa0JBQWtCQyxnQkFDakJBLGdCQUFnQixDQUFDLENBQUM7b0JBQ3JCQyxtQkFBbUIsQ0FBQztvQkFDcEIsSUFBSUMsd0JBQXdCQztvQkFDNUIsSUFBSTt3QkFDRkMsR0FBRzs0QkFDREMsY0FBY2Q7NEJBQ2QsSUFDRWUsY0FBY0MsS0FBS0MsWUFDbkIsU0FBU0YsZUFDVCxDQUNFQSxDQUFBQSxZQUFZRyxjQUFjLEdBQUdsQixlQUM3Qm1CLG1CQUFrQixHQUdwQjtnQ0FDQSxJQUFJQyxXQUFXTCxZQUFZSyxRQUFRO2dDQUNuQyxJQUFJLGVBQWUsT0FBT0EsVUFBVTtvQ0FDbENMLFlBQVlLLFFBQVEsR0FBRztvQ0FDdkJSLHVCQUF1QkcsWUFBWU0sYUFBYTtvQ0FDaEQsSUFBSUMsdUJBQXVCRixTQUN6QkwsWUFBWUcsY0FBYyxJQUFJbEI7b0NBRWhDQSxjQUFjQyxRQUFRQyxZQUFZO29DQUNsQyxJQUFJLGVBQWUsT0FBT29CLHNCQUFzQjt3Q0FDOUNQLFlBQVlLLFFBQVEsR0FBR0U7d0NBQ3ZCUixjQUFjZDt3Q0FDZEksY0FBYyxDQUFDO3dDQUNmLE1BQU1TO29DQUNSO29DQUNBRSxnQkFBZ0JDLEtBQUtDLGNBQWNNLElBQUlOO29DQUN2Q0gsY0FBY2Q7Z0NBQ2hCLE9BQU91QixJQUFJTjtnQ0FDWEYsY0FBY0MsS0FBS0M7NEJBQ3JCOzRCQUNBLElBQUksU0FBU0YsYUFBYVgsY0FBYyxDQUFDO2lDQUNwQztnQ0FDSCxJQUFJb0IsYUFBYVIsS0FBS1M7Z0NBQ3RCLFNBQVNELGNBQ1BFLG1CQUNFQyxlQUNBSCxXQUFXckIsU0FBUyxHQUFHSDtnQ0FFM0JJLGNBQWMsQ0FBQzs0QkFDakI7d0JBQ0Y7d0JBQ0EsTUFBTUM7b0JBQ1IsU0FBVTt3QkFDUFUsY0FBYyxNQUNaSCx1QkFBdUJELHVCQUN2QkQsbUJBQW1CLENBQUM7b0JBQ3pCO29CQUNBTixjQUFjLEtBQUs7Z0JBQ3JCO1lBQ0YsU0FBVTtnQkFDUkEsY0FDSXdCLHFDQUNDN0IsdUJBQXVCLENBQUM7WUFDL0I7UUFDRjtJQUNGO0lBQ0EsU0FBUzhCLEtBQUtDLElBQUksRUFBRUMsSUFBSTtRQUN0QixJQUFJQyxRQUFRRixLQUFLRyxNQUFNO1FBQ3ZCSCxLQUFLRCxJQUFJLENBQUNFO1FBQ1YxQixHQUFHLE1BQU8sSUFBSTJCLE9BQVM7WUFDckIsSUFBSUUsY0FBYyxRQUFTLE1BQU8sR0FDaENDLFNBQVNMLElBQUksQ0FBQ0ksWUFBWTtZQUM1QixJQUFJLElBQUlFLFFBQVFELFFBQVFKLE9BQ3RCLElBQUssQ0FBQ0csWUFBWSxHQUFHSCxNQUNsQkQsSUFBSSxDQUFDRSxNQUFNLEdBQUdHLFFBQ2RILFFBQVFFO2lCQUNSLE1BQU03QjtRQUNiO0lBQ0Y7SUFDQSxTQUFTVyxLQUFLYyxJQUFJO1FBQ2hCLE9BQU8sTUFBTUEsS0FBS0csTUFBTSxHQUFHLE9BQU9ILElBQUksQ0FBQyxFQUFFO0lBQzNDO0lBQ0EsU0FBU1AsSUFBSU8sSUFBSTtRQUNmLElBQUksTUFBTUEsS0FBS0csTUFBTSxFQUFFLE9BQU87UUFDOUIsSUFBSUksUUFBUVAsSUFBSSxDQUFDLEVBQUUsRUFDakJRLE9BQU9SLEtBQUtQLEdBQUc7UUFDakIsSUFBSWUsU0FBU0QsT0FBTztZQUNsQlAsSUFBSSxDQUFDLEVBQUUsR0FBR1E7WUFDVmpDLEdBQUcsSUFDRCxJQUFJMkIsUUFBUSxHQUFHQyxTQUFTSCxLQUFLRyxNQUFNLEVBQUVNLGFBQWFOLFdBQVcsR0FDN0RELFFBQVFPLFlBRVI7Z0JBQ0EsSUFBSUMsWUFBWSxJQUFLUixDQUFBQSxRQUFRLEtBQUssR0FDaENTLE9BQU9YLElBQUksQ0FBQ1UsVUFBVSxFQUN0QkUsYUFBYUYsWUFBWSxHQUN6QkcsUUFBUWIsSUFBSSxDQUFDWSxXQUFXO2dCQUMxQixJQUFJLElBQUlOLFFBQVFLLE1BQU1ILE9BQ3BCSSxhQUFhVCxVQUFVLElBQUlHLFFBQVFPLE9BQU9GLFFBQ3JDLEtBQUssQ0FBQ1QsTUFBTSxHQUFHVyxPQUNmYixJQUFJLENBQUNZLFdBQVcsR0FBR0osTUFDbkJOLFFBQVFVLFVBQVUsSUFDbEIsS0FBSyxDQUFDVixNQUFNLEdBQUdTLE1BQ2ZYLElBQUksQ0FBQ1UsVUFBVSxHQUFHRixNQUNsQk4sUUFBUVEsU0FBUztxQkFDbkIsSUFBSUUsYUFBYVQsVUFBVSxJQUFJRyxRQUFRTyxPQUFPTCxPQUNqRCxJQUFLLENBQUNOLE1BQU0sR0FBR1csT0FDWmIsSUFBSSxDQUFDWSxXQUFXLEdBQUdKLE1BQ25CTixRQUFRVTtxQkFDUixNQUFNckM7WUFDYjtRQUNGO1FBQ0EsT0FBT2dDO0lBQ1Q7SUFDQSxTQUFTRCxRQUFRL0IsQ0FBQyxFQUFFUSxDQUFDO1FBQ25CLElBQUkrQixPQUFPdkMsRUFBRXdDLFNBQVMsR0FBR2hDLEVBQUVnQyxTQUFTO1FBQ3BDLE9BQU8sTUFBTUQsT0FBT0EsT0FBT3ZDLEVBQUV5QyxFQUFFLEdBQUdqQyxFQUFFaUMsRUFBRTtJQUN4QztJQUNBLFNBQVNoQyxjQUFjZCxXQUFXO1FBQ2hDLElBQUssSUFBSStDLFFBQVEvQixLQUFLUyxhQUFhLFNBQVNzQixPQUFTO1lBQ25ELElBQUksU0FBU0EsTUFBTTNCLFFBQVEsRUFBRUcsSUFBSUU7aUJBQzVCLElBQUlzQixNQUFNNUMsU0FBUyxJQUFJSCxhQUMxQnVCLElBQUlFLGFBQ0RzQixNQUFNRixTQUFTLEdBQUdFLE1BQU03QixjQUFjLEVBQ3ZDVyxLQUFLWixXQUFXOEI7aUJBQ2Y7WUFDTEEsUUFBUS9CLEtBQUtTO1FBQ2Y7SUFDRjtJQUNBLFNBQVNFLGNBQWMzQixXQUFXO1FBQ2hDTyx5QkFBeUIsQ0FBQztRQUMxQk8sY0FBY2Q7UUFDZCxJQUFJLENBQUNNLHlCQUNILElBQUksU0FBU1UsS0FBS0MsWUFDaEIsMEJBQTJCLENBQUMsR0FBSStCO2FBQzdCO1lBQ0gsSUFBSXhCLGFBQWFSLEtBQUtTO1lBQ3RCLFNBQVNELGNBQ1BFLG1CQUNFQyxlQUNBSCxXQUFXckIsU0FBUyxHQUFHSDtRQUU3QjtJQUNKO0lBQ0EsU0FBU21CO1FBQ1AsT0FBT2xCLFFBQVFDLFlBQVksS0FBS0MsWUFBWThDLGdCQUFnQixDQUFDLElBQUksQ0FBQztJQUNwRTtJQUNBLFNBQVNEO1FBQ1BqRCx3QkFDRyx3QkFBd0IsQ0FBQyxHQUFJNkIsa0NBQWlDO0lBQ25FO0lBQ0EsU0FBU0YsbUJBQW1CTixRQUFRLEVBQUU4QixFQUFFO1FBQ3RDekMsZ0JBQWdCMEMsZ0JBQWdCO1lBQzlCL0IsU0FBU25CLFFBQVFDLFlBQVk7UUFDL0IsR0FBR2dEO0lBQ0w7SUFDQSxnQkFBZ0IsT0FBT0Usa0NBQ3JCLGVBQ0UsT0FBT0EsK0JBQStCQywyQkFBMkIsSUFDbkVELCtCQUErQkMsMkJBQTJCLENBQUNDO0lBQzdEckQsb0JBQW9CLEdBQUcsS0FBSztJQUM1QixJQUNFLGFBQWEsT0FBT3NELGVBQ3BCLGVBQWUsT0FBT0EsWUFBWUMsR0FBRyxFQUNyQztRQUNBLElBQUlDLG1CQUFtQkY7UUFDdkJ0RCxvQkFBb0IsR0FBRztZQUNyQixPQUFPd0QsaUJBQWlCRCxHQUFHO1FBQzdCO0lBQ0YsT0FBTztRQUNMLElBQUlFLFlBQVlDLE1BQ2RDLGNBQWNGLFVBQVVGLEdBQUc7UUFDN0J2RCxvQkFBb0IsR0FBRztZQUNyQixPQUFPeUQsVUFBVUYsR0FBRyxLQUFLSTtRQUMzQjtJQUNGO0lBQ0EsSUFBSTNDLFlBQVksRUFBRSxFQUNoQlEsYUFBYSxFQUFFLEVBQ2ZvQyxnQkFBZ0IsR0FDaEI5QyxjQUFjLE1BQ2RILHVCQUF1QixHQUN2QkYsbUJBQW1CLENBQUMsR0FDcEJKLDBCQUEwQixDQUFDLEdBQzNCQyx5QkFBeUIsQ0FBQyxHQUMxQjRDLGtCQUFrQixlQUFlLE9BQU9XLGFBQWFBLGFBQWEsTUFDbEV0RCxvQkFDRSxlQUFlLE9BQU91RCxlQUFlQSxlQUFlLE1BQ3REQyxvQkFDRSxnQkFBZ0IsT0FBT0MsZUFBZUEsZUFBZSxNQUN2RGxFLHVCQUF1QixDQUFDLEdBQ3hCVSxnQkFBZ0IsQ0FBQyxHQUNqQndDLGdCQUFnQixHQUNoQjlDLFlBQVksQ0FBQztJQUNmLElBQUksZUFBZSxPQUFPNkQsbUJBQ3hCLElBQUlwQyxtQ0FBbUM7UUFDckNvQyxrQkFBa0JsRTtJQUNwQjtTQUNHLElBQUksZ0JBQWdCLE9BQU9vRSxnQkFBZ0I7UUFDOUMsSUFBSUMsVUFBVSxJQUFJRCxrQkFDaEJFLE9BQU9ELFFBQVFFLEtBQUs7UUFDdEJGLFFBQVFHLEtBQUssQ0FBQ0MsU0FBUyxHQUFHekU7UUFDMUI4QixtQ0FBbUM7WUFDakN3QyxLQUFLSSxXQUFXLENBQUM7UUFDbkI7SUFDRixPQUNFNUMsbUNBQW1DO1FBQ2pDdUIsZ0JBQWdCckQsMEJBQTBCO0lBQzVDO0lBQ0ZHLDZCQUE2QixHQUFHO0lBQ2hDQSxrQ0FBa0MsR0FBRztJQUNyQ0EsNEJBQTRCLEdBQUc7SUFDL0JBLCtCQUErQixHQUFHO0lBQ2xDQSwwQkFBMEIsR0FBRztJQUM3QkEscUNBQXFDLEdBQUc7SUFDeENBLCtCQUErQixHQUFHLFNBQVUrRSxJQUFJO1FBQzlDQSxLQUFLNUQsUUFBUSxHQUFHO0lBQ2xCO0lBQ0FuQixrQ0FBa0MsR0FBRztRQUNuQ0ssMkJBQ0VJLG9CQUNDLDJCQUEyQixDQUFDLEdBQUlzQyxxQkFBb0I7SUFDekQ7SUFDQS9DLCtCQUErQixHQUFHLFNBQVVrRixHQUFHO1FBQzdDLElBQUlBLE9BQU8sTUFBTUEsTUFDYkMsUUFBUUMsS0FBSyxDQUNYLHFIQUVEcEMsZ0JBQWdCLElBQUlrQyxNQUFNRyxLQUFLQyxLQUFLLENBQUMsTUFBTUosT0FBTztJQUN6RDtJQUNBbEYsd0NBQXdDLEdBQUc7UUFDekMsT0FBT1c7SUFDVDtJQUNBWCxxQ0FBcUMsR0FBRztRQUN0QyxPQUFPZSxLQUFLQztJQUNkO0lBQ0FoQixxQkFBcUIsR0FBRyxTQUFVMEYsWUFBWTtRQUM1QyxPQUFRL0U7WUFDTixLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsSUFBSVMsZ0JBQWdCO2dCQUNwQjtZQUNGO2dCQUNFQSxnQkFBZ0JUO1FBQ3BCO1FBQ0EsSUFBSUQsd0JBQXdCQztRQUM1QkEsdUJBQXVCUztRQUN2QixJQUFJO1lBQ0YsT0FBT3NFO1FBQ1QsU0FBVTtZQUNSL0UsdUJBQXVCRDtRQUN6QjtJQUNGO0lBQ0FWLCtCQUErQixHQUFHLFlBQWE7SUFDL0NBLDZCQUE2QixHQUFHLFlBQWE7SUFDN0NBLGdDQUFnQyxHQUFHLFNBQVVvQixhQUFhLEVBQUVzRSxZQUFZO1FBQ3RFLE9BQVF0RTtZQUNOLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO2dCQUNIO1lBQ0Y7Z0JBQ0VBLGdCQUFnQjtRQUNwQjtRQUNBLElBQUlWLHdCQUF3QkM7UUFDNUJBLHVCQUF1QlM7UUFDdkIsSUFBSTtZQUNGLE9BQU9zRTtRQUNULFNBQVU7WUFDUi9FLHVCQUF1QkQ7UUFDekI7SUFDRjtJQUNBVixpQ0FBaUMsR0FBRyxTQUNsQ29CLGFBQWEsRUFDYkQsUUFBUSxFQUNSNEUsT0FBTztRQUVQLElBQUloRyxjQUFjQyxRQUFRQyxZQUFZO1FBQ3RDLGFBQWEsT0FBTzhGLFdBQVcsU0FBU0EsVUFDbkMsV0FBV0EsUUFBUUMsS0FBSyxFQUN4QkQsVUFDQyxhQUFhLE9BQU9BLFdBQVcsSUFBSUEsVUFDL0JoRyxjQUFjZ0csVUFDZGhHLFdBQVcsSUFDaEJnRyxVQUFVaEc7UUFDZixPQUFRcUI7WUFDTixLQUFLO2dCQUNILElBQUk2RSxVQUFVLENBQUM7Z0JBQ2Y7WUFDRixLQUFLO2dCQUNIQSxVQUFVO2dCQUNWO1lBQ0YsS0FBSztnQkFDSEEsVUFBVTtnQkFDVjtZQUNGLEtBQUs7Z0JBQ0hBLFVBQVU7Z0JBQ1Y7WUFDRjtnQkFDRUEsVUFBVTtRQUNkO1FBQ0FBLFVBQVVGLFVBQVVFO1FBQ3BCN0UsZ0JBQWdCO1lBQ2R5QixJQUFJZTtZQUNKekMsVUFBVUE7WUFDVkMsZUFBZUE7WUFDZmxCLFdBQVc2RjtZQUNYOUUsZ0JBQWdCZ0Y7WUFDaEJyRCxXQUFXLENBQUM7UUFDZDtRQUNBbUQsVUFBVWhHLGNBQ0wsZUFBZTZDLFNBQVMsR0FBR21ELFNBQzVCbkUsS0FBS0osWUFBWUosZ0JBQ2pCLFNBQVNMLEtBQUtDLGNBQ1pJLGtCQUFrQkwsS0FBS1MsZUFDdEJsQixDQUFBQSx5QkFDSUMsQ0FBQUEsa0JBQWtCQyxnQkFBaUJBLGdCQUFnQixDQUFDLENBQUMsSUFDckRGLHlCQUF5QixDQUFDLEdBQy9CbUIsbUJBQW1CQyxlQUFlcUUsVUFBVWhHLFlBQVcsQ0FBQyxJQUN6RCxlQUFlNkMsU0FBUyxHQUFHcUQsU0FDNUJyRSxLQUFLWixXQUFXSSxnQkFDaEJmLDJCQUNFSSxvQkFDQywyQkFBMkIsQ0FBQyxHQUFJc0MscUJBQW9CLENBQUM7UUFDNUQsT0FBTzNCO0lBQ1Q7SUFDQXBCLDRCQUE0QixHQUFHa0I7SUFDL0JsQiw2QkFBNkIsR0FBRyxTQUFVbUIsUUFBUTtRQUNoRCxJQUFJaUYsc0JBQXNCekY7UUFDMUIsT0FBTztZQUNMLElBQUlELHdCQUF3QkM7WUFDNUJBLHVCQUF1QnlGO1lBQ3ZCLElBQUk7Z0JBQ0YsT0FBT2pGLFNBQVNrRixLQUFLLENBQUMsSUFBSSxFQUFFQztZQUM5QixTQUFVO2dCQUNSM0YsdUJBQXVCRDtZQUN6QjtRQUNGO0lBQ0Y7SUFDQSxnQkFBZ0IsT0FBT3lDLGtDQUNyQixlQUNFLE9BQU9BLCtCQUErQm9ELDBCQUEwQixJQUNsRXBELCtCQUErQm9ELDBCQUEwQixDQUFDbEQ7QUFDOUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90Yi13ZWJzaXRlLy4vbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9maWJlci9ub2RlX21vZHVsZXMvc2NoZWR1bGVyL2Nqcy9zY2hlZHVsZXIuZGV2ZWxvcG1lbnQuanM/YjExNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiBzY2hlZHVsZXIuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gcGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lKCkge1xuICAgICAgaWYgKGlzTWVzc2FnZUxvb3BSdW5uaW5nKSB7XG4gICAgICAgIHZhciBjdXJyZW50VGltZSA9IGV4cG9ydHMudW5zdGFibGVfbm93KCk7XG4gICAgICAgIHN0YXJ0VGltZSA9IGN1cnJlbnRUaW1lO1xuICAgICAgICB2YXIgaGFzTW9yZVdvcmsgPSAhMDtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBhOiB7XG4gICAgICAgICAgICBpc0hvc3RDYWxsYmFja1NjaGVkdWxlZCA9ICExO1xuICAgICAgICAgICAgaXNIb3N0VGltZW91dFNjaGVkdWxlZCAmJlxuICAgICAgICAgICAgICAoKGlzSG9zdFRpbWVvdXRTY2hlZHVsZWQgPSAhMSksXG4gICAgICAgICAgICAgIGxvY2FsQ2xlYXJUaW1lb3V0KHRhc2tUaW1lb3V0SUQpLFxuICAgICAgICAgICAgICAodGFza1RpbWVvdXRJRCA9IC0xKSk7XG4gICAgICAgICAgICBpc1BlcmZvcm1pbmdXb3JrID0gITA7XG4gICAgICAgICAgICB2YXIgcHJldmlvdXNQcmlvcml0eUxldmVsID0gY3VycmVudFByaW9yaXR5TGV2ZWw7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBiOiB7XG4gICAgICAgICAgICAgICAgYWR2YW5jZVRpbWVycyhjdXJyZW50VGltZSk7XG4gICAgICAgICAgICAgICAgZm9yIChcbiAgICAgICAgICAgICAgICAgIGN1cnJlbnRUYXNrID0gcGVlayh0YXNrUXVldWUpO1xuICAgICAgICAgICAgICAgICAgbnVsbCAhPT0gY3VycmVudFRhc2sgJiZcbiAgICAgICAgICAgICAgICAgICEoXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUYXNrLmV4cGlyYXRpb25UaW1lID4gY3VycmVudFRpbWUgJiZcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkWWllbGRUb0hvc3QoKVxuICAgICAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgdmFyIGNhbGxiYWNrID0gY3VycmVudFRhc2suY2FsbGJhY2s7XG4gICAgICAgICAgICAgICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgY2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRhc2suY2FsbGJhY2sgPSBudWxsO1xuICAgICAgICAgICAgICAgICAgICBjdXJyZW50UHJpb3JpdHlMZXZlbCA9IGN1cnJlbnRUYXNrLnByaW9yaXR5TGV2ZWw7XG4gICAgICAgICAgICAgICAgICAgIHZhciBjb250aW51YXRpb25DYWxsYmFjayA9IGNhbGxiYWNrKFxuICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUYXNrLmV4cGlyYXRpb25UaW1lIDw9IGN1cnJlbnRUaW1lXG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUaW1lID0gZXhwb3J0cy51bnN0YWJsZV9ub3coKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIGNvbnRpbnVhdGlvbkNhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgY3VycmVudFRhc2suY2FsbGJhY2sgPSBjb250aW51YXRpb25DYWxsYmFjaztcbiAgICAgICAgICAgICAgICAgICAgICBhZHZhbmNlVGltZXJzKGN1cnJlbnRUaW1lKTtcbiAgICAgICAgICAgICAgICAgICAgICBoYXNNb3JlV29yayA9ICEwO1xuICAgICAgICAgICAgICAgICAgICAgIGJyZWFrIGI7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFRhc2sgPT09IHBlZWsodGFza1F1ZXVlKSAmJiBwb3AodGFza1F1ZXVlKTtcbiAgICAgICAgICAgICAgICAgICAgYWR2YW5jZVRpbWVycyhjdXJyZW50VGltZSk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2UgcG9wKHRhc2tRdWV1ZSk7XG4gICAgICAgICAgICAgICAgICBjdXJyZW50VGFzayA9IHBlZWsodGFza1F1ZXVlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKG51bGwgIT09IGN1cnJlbnRUYXNrKSBoYXNNb3JlV29yayA9ICEwO1xuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgdmFyIGZpcnN0VGltZXIgPSBwZWVrKHRpbWVyUXVldWUpO1xuICAgICAgICAgICAgICAgICAgbnVsbCAhPT0gZmlyc3RUaW1lciAmJlxuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0SG9zdFRpbWVvdXQoXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVGltZW91dCxcbiAgICAgICAgICAgICAgICAgICAgICBmaXJzdFRpbWVyLnN0YXJ0VGltZSAtIGN1cnJlbnRUaW1lXG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICBoYXNNb3JlV29yayA9ICExO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBicmVhayBhO1xuICAgICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgKGN1cnJlbnRUYXNrID0gbnVsbCksXG4gICAgICAgICAgICAgICAgKGN1cnJlbnRQcmlvcml0eUxldmVsID0gcHJldmlvdXNQcmlvcml0eUxldmVsKSxcbiAgICAgICAgICAgICAgICAoaXNQZXJmb3JtaW5nV29yayA9ICExKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGhhc01vcmVXb3JrID0gdm9pZCAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICBoYXNNb3JlV29ya1xuICAgICAgICAgICAgPyBzY2hlZHVsZVBlcmZvcm1Xb3JrVW50aWxEZWFkbGluZSgpXG4gICAgICAgICAgICA6IChpc01lc3NhZ2VMb29wUnVubmluZyA9ICExKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBwdXNoKGhlYXAsIG5vZGUpIHtcbiAgICAgIHZhciBpbmRleCA9IGhlYXAubGVuZ3RoO1xuICAgICAgaGVhcC5wdXNoKG5vZGUpO1xuICAgICAgYTogZm9yICg7IDAgPCBpbmRleDsgKSB7XG4gICAgICAgIHZhciBwYXJlbnRJbmRleCA9IChpbmRleCAtIDEpID4+PiAxLFxuICAgICAgICAgIHBhcmVudCA9IGhlYXBbcGFyZW50SW5kZXhdO1xuICAgICAgICBpZiAoMCA8IGNvbXBhcmUocGFyZW50LCBub2RlKSlcbiAgICAgICAgICAoaGVhcFtwYXJlbnRJbmRleF0gPSBub2RlKSxcbiAgICAgICAgICAgIChoZWFwW2luZGV4XSA9IHBhcmVudCksXG4gICAgICAgICAgICAoaW5kZXggPSBwYXJlbnRJbmRleCk7XG4gICAgICAgIGVsc2UgYnJlYWsgYTtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gcGVlayhoZWFwKSB7XG4gICAgICByZXR1cm4gMCA9PT0gaGVhcC5sZW5ndGggPyBudWxsIDogaGVhcFswXTtcbiAgICB9XG4gICAgZnVuY3Rpb24gcG9wKGhlYXApIHtcbiAgICAgIGlmICgwID09PSBoZWFwLmxlbmd0aCkgcmV0dXJuIG51bGw7XG4gICAgICB2YXIgZmlyc3QgPSBoZWFwWzBdLFxuICAgICAgICBsYXN0ID0gaGVhcC5wb3AoKTtcbiAgICAgIGlmIChsYXN0ICE9PSBmaXJzdCkge1xuICAgICAgICBoZWFwWzBdID0gbGFzdDtcbiAgICAgICAgYTogZm9yIChcbiAgICAgICAgICB2YXIgaW5kZXggPSAwLCBsZW5ndGggPSBoZWFwLmxlbmd0aCwgaGFsZkxlbmd0aCA9IGxlbmd0aCA+Pj4gMTtcbiAgICAgICAgICBpbmRleCA8IGhhbGZMZW5ndGg7XG5cbiAgICAgICAgKSB7XG4gICAgICAgICAgdmFyIGxlZnRJbmRleCA9IDIgKiAoaW5kZXggKyAxKSAtIDEsXG4gICAgICAgICAgICBsZWZ0ID0gaGVhcFtsZWZ0SW5kZXhdLFxuICAgICAgICAgICAgcmlnaHRJbmRleCA9IGxlZnRJbmRleCArIDEsXG4gICAgICAgICAgICByaWdodCA9IGhlYXBbcmlnaHRJbmRleF07XG4gICAgICAgICAgaWYgKDAgPiBjb21wYXJlKGxlZnQsIGxhc3QpKVxuICAgICAgICAgICAgcmlnaHRJbmRleCA8IGxlbmd0aCAmJiAwID4gY29tcGFyZShyaWdodCwgbGVmdClcbiAgICAgICAgICAgICAgPyAoKGhlYXBbaW5kZXhdID0gcmlnaHQpLFxuICAgICAgICAgICAgICAgIChoZWFwW3JpZ2h0SW5kZXhdID0gbGFzdCksXG4gICAgICAgICAgICAgICAgKGluZGV4ID0gcmlnaHRJbmRleCkpXG4gICAgICAgICAgICAgIDogKChoZWFwW2luZGV4XSA9IGxlZnQpLFxuICAgICAgICAgICAgICAgIChoZWFwW2xlZnRJbmRleF0gPSBsYXN0KSxcbiAgICAgICAgICAgICAgICAoaW5kZXggPSBsZWZ0SW5kZXgpKTtcbiAgICAgICAgICBlbHNlIGlmIChyaWdodEluZGV4IDwgbGVuZ3RoICYmIDAgPiBjb21wYXJlKHJpZ2h0LCBsYXN0KSlcbiAgICAgICAgICAgIChoZWFwW2luZGV4XSA9IHJpZ2h0KSxcbiAgICAgICAgICAgICAgKGhlYXBbcmlnaHRJbmRleF0gPSBsYXN0KSxcbiAgICAgICAgICAgICAgKGluZGV4ID0gcmlnaHRJbmRleCk7XG4gICAgICAgICAgZWxzZSBicmVhayBhO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gZmlyc3Q7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGNvbXBhcmUoYSwgYikge1xuICAgICAgdmFyIGRpZmYgPSBhLnNvcnRJbmRleCAtIGIuc29ydEluZGV4O1xuICAgICAgcmV0dXJuIDAgIT09IGRpZmYgPyBkaWZmIDogYS5pZCAtIGIuaWQ7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGFkdmFuY2VUaW1lcnMoY3VycmVudFRpbWUpIHtcbiAgICAgIGZvciAodmFyIHRpbWVyID0gcGVlayh0aW1lclF1ZXVlKTsgbnVsbCAhPT0gdGltZXI7ICkge1xuICAgICAgICBpZiAobnVsbCA9PT0gdGltZXIuY2FsbGJhY2spIHBvcCh0aW1lclF1ZXVlKTtcbiAgICAgICAgZWxzZSBpZiAodGltZXIuc3RhcnRUaW1lIDw9IGN1cnJlbnRUaW1lKVxuICAgICAgICAgIHBvcCh0aW1lclF1ZXVlKSxcbiAgICAgICAgICAgICh0aW1lci5zb3J0SW5kZXggPSB0aW1lci5leHBpcmF0aW9uVGltZSksXG4gICAgICAgICAgICBwdXNoKHRhc2tRdWV1ZSwgdGltZXIpO1xuICAgICAgICBlbHNlIGJyZWFrO1xuICAgICAgICB0aW1lciA9IHBlZWsodGltZXJRdWV1ZSk7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhbmRsZVRpbWVvdXQoY3VycmVudFRpbWUpIHtcbiAgICAgIGlzSG9zdFRpbWVvdXRTY2hlZHVsZWQgPSAhMTtcbiAgICAgIGFkdmFuY2VUaW1lcnMoY3VycmVudFRpbWUpO1xuICAgICAgaWYgKCFpc0hvc3RDYWxsYmFja1NjaGVkdWxlZClcbiAgICAgICAgaWYgKG51bGwgIT09IHBlZWsodGFza1F1ZXVlKSlcbiAgICAgICAgICAoaXNIb3N0Q2FsbGJhY2tTY2hlZHVsZWQgPSAhMCksIHJlcXVlc3RIb3N0Q2FsbGJhY2soKTtcbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgdmFyIGZpcnN0VGltZXIgPSBwZWVrKHRpbWVyUXVldWUpO1xuICAgICAgICAgIG51bGwgIT09IGZpcnN0VGltZXIgJiZcbiAgICAgICAgICAgIHJlcXVlc3RIb3N0VGltZW91dChcbiAgICAgICAgICAgICAgaGFuZGxlVGltZW91dCxcbiAgICAgICAgICAgICAgZmlyc3RUaW1lci5zdGFydFRpbWUgLSBjdXJyZW50VGltZVxuICAgICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBzaG91bGRZaWVsZFRvSG9zdCgpIHtcbiAgICAgIHJldHVybiBleHBvcnRzLnVuc3RhYmxlX25vdygpIC0gc3RhcnRUaW1lIDwgZnJhbWVJbnRlcnZhbCA/ICExIDogITA7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHJlcXVlc3RIb3N0Q2FsbGJhY2soKSB7XG4gICAgICBpc01lc3NhZ2VMb29wUnVubmluZyB8fFxuICAgICAgICAoKGlzTWVzc2FnZUxvb3BSdW5uaW5nID0gITApLCBzY2hlZHVsZVBlcmZvcm1Xb3JrVW50aWxEZWFkbGluZSgpKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gcmVxdWVzdEhvc3RUaW1lb3V0KGNhbGxiYWNrLCBtcykge1xuICAgICAgdGFza1RpbWVvdXRJRCA9IGxvY2FsU2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICAgIGNhbGxiYWNrKGV4cG9ydHMudW5zdGFibGVfbm93KCkpO1xuICAgICAgfSwgbXMpO1xuICAgIH1cbiAgICBcInVuZGVmaW5lZFwiICE9PSB0eXBlb2YgX19SRUFDVF9ERVZUT09MU19HTE9CQUxfSE9PS19fICYmXG4gICAgICBcImZ1bmN0aW9uXCIgPT09XG4gICAgICAgIHR5cGVvZiBfX1JFQUNUX0RFVlRPT0xTX0dMT0JBTF9IT09LX18ucmVnaXN0ZXJJbnRlcm5hbE1vZHVsZVN0YXJ0ICYmXG4gICAgICBfX1JFQUNUX0RFVlRPT0xTX0dMT0JBTF9IT09LX18ucmVnaXN0ZXJJbnRlcm5hbE1vZHVsZVN0YXJ0KEVycm9yKCkpO1xuICAgIGV4cG9ydHMudW5zdGFibGVfbm93ID0gdm9pZCAwO1xuICAgIGlmIChcbiAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiBwZXJmb3JtYW5jZSAmJlxuICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgcGVyZm9ybWFuY2Uubm93XG4gICAgKSB7XG4gICAgICB2YXIgbG9jYWxQZXJmb3JtYW5jZSA9IHBlcmZvcm1hbmNlO1xuICAgICAgZXhwb3J0cy51bnN0YWJsZV9ub3cgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBsb2NhbFBlcmZvcm1hbmNlLm5vdygpO1xuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgdmFyIGxvY2FsRGF0ZSA9IERhdGUsXG4gICAgICAgIGluaXRpYWxUaW1lID0gbG9jYWxEYXRlLm5vdygpO1xuICAgICAgZXhwb3J0cy51bnN0YWJsZV9ub3cgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBsb2NhbERhdGUubm93KCkgLSBpbml0aWFsVGltZTtcbiAgICAgIH07XG4gICAgfVxuICAgIHZhciB0YXNrUXVldWUgPSBbXSxcbiAgICAgIHRpbWVyUXVldWUgPSBbXSxcbiAgICAgIHRhc2tJZENvdW50ZXIgPSAxLFxuICAgICAgY3VycmVudFRhc2sgPSBudWxsLFxuICAgICAgY3VycmVudFByaW9yaXR5TGV2ZWwgPSAzLFxuICAgICAgaXNQZXJmb3JtaW5nV29yayA9ICExLFxuICAgICAgaXNIb3N0Q2FsbGJhY2tTY2hlZHVsZWQgPSAhMSxcbiAgICAgIGlzSG9zdFRpbWVvdXRTY2hlZHVsZWQgPSAhMSxcbiAgICAgIGxvY2FsU2V0VGltZW91dCA9IFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHNldFRpbWVvdXQgPyBzZXRUaW1lb3V0IDogbnVsbCxcbiAgICAgIGxvY2FsQ2xlYXJUaW1lb3V0ID1cbiAgICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgY2xlYXJUaW1lb3V0ID8gY2xlYXJUaW1lb3V0IDogbnVsbCxcbiAgICAgIGxvY2FsU2V0SW1tZWRpYXRlID1cbiAgICAgICAgXCJ1bmRlZmluZWRcIiAhPT0gdHlwZW9mIHNldEltbWVkaWF0ZSA/IHNldEltbWVkaWF0ZSA6IG51bGwsXG4gICAgICBpc01lc3NhZ2VMb29wUnVubmluZyA9ICExLFxuICAgICAgdGFza1RpbWVvdXRJRCA9IC0xLFxuICAgICAgZnJhbWVJbnRlcnZhbCA9IDUsXG4gICAgICBzdGFydFRpbWUgPSAtMTtcbiAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgbG9jYWxTZXRJbW1lZGlhdGUpXG4gICAgICB2YXIgc2NoZWR1bGVQZXJmb3JtV29ya1VudGlsRGVhZGxpbmUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGxvY2FsU2V0SW1tZWRpYXRlKHBlcmZvcm1Xb3JrVW50aWxEZWFkbGluZSk7XG4gICAgICB9O1xuICAgIGVsc2UgaWYgKFwidW5kZWZpbmVkXCIgIT09IHR5cGVvZiBNZXNzYWdlQ2hhbm5lbCkge1xuICAgICAgdmFyIGNoYW5uZWwgPSBuZXcgTWVzc2FnZUNoYW5uZWwoKSxcbiAgICAgICAgcG9ydCA9IGNoYW5uZWwucG9ydDI7XG4gICAgICBjaGFubmVsLnBvcnQxLm9ubWVzc2FnZSA9IHBlcmZvcm1Xb3JrVW50aWxEZWFkbGluZTtcbiAgICAgIHNjaGVkdWxlUGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lID0gZnVuY3Rpb24gKCkge1xuICAgICAgICBwb3J0LnBvc3RNZXNzYWdlKG51bGwpO1xuICAgICAgfTtcbiAgICB9IGVsc2VcbiAgICAgIHNjaGVkdWxlUGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lID0gZnVuY3Rpb24gKCkge1xuICAgICAgICBsb2NhbFNldFRpbWVvdXQocGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lLCAwKTtcbiAgICAgIH07XG4gICAgZXhwb3J0cy51bnN0YWJsZV9JZGxlUHJpb3JpdHkgPSA1O1xuICAgIGV4cG9ydHMudW5zdGFibGVfSW1tZWRpYXRlUHJpb3JpdHkgPSAxO1xuICAgIGV4cG9ydHMudW5zdGFibGVfTG93UHJpb3JpdHkgPSA0O1xuICAgIGV4cG9ydHMudW5zdGFibGVfTm9ybWFsUHJpb3JpdHkgPSAzO1xuICAgIGV4cG9ydHMudW5zdGFibGVfUHJvZmlsaW5nID0gbnVsbDtcbiAgICBleHBvcnRzLnVuc3RhYmxlX1VzZXJCbG9ja2luZ1ByaW9yaXR5ID0gMjtcbiAgICBleHBvcnRzLnVuc3RhYmxlX2NhbmNlbENhbGxiYWNrID0gZnVuY3Rpb24gKHRhc2spIHtcbiAgICAgIHRhc2suY2FsbGJhY2sgPSBudWxsO1xuICAgIH07XG4gICAgZXhwb3J0cy51bnN0YWJsZV9jb250aW51ZUV4ZWN1dGlvbiA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIGlzSG9zdENhbGxiYWNrU2NoZWR1bGVkIHx8XG4gICAgICAgIGlzUGVyZm9ybWluZ1dvcmsgfHxcbiAgICAgICAgKChpc0hvc3RDYWxsYmFja1NjaGVkdWxlZCA9ICEwKSwgcmVxdWVzdEhvc3RDYWxsYmFjaygpKTtcbiAgICB9O1xuICAgIGV4cG9ydHMudW5zdGFibGVfZm9yY2VGcmFtZVJhdGUgPSBmdW5jdGlvbiAoZnBzKSB7XG4gICAgICAwID4gZnBzIHx8IDEyNSA8IGZwc1xuICAgICAgICA/IGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcImZvcmNlRnJhbWVSYXRlIHRha2VzIGEgcG9zaXRpdmUgaW50IGJldHdlZW4gMCBhbmQgMTI1LCBmb3JjaW5nIGZyYW1lIHJhdGVzIGhpZ2hlciB0aGFuIDEyNSBmcHMgaXMgbm90IHN1cHBvcnRlZFwiXG4gICAgICAgICAgKVxuICAgICAgICA6IChmcmFtZUludGVydmFsID0gMCA8IGZwcyA/IE1hdGguZmxvb3IoMWUzIC8gZnBzKSA6IDUpO1xuICAgIH07XG4gICAgZXhwb3J0cy51bnN0YWJsZV9nZXRDdXJyZW50UHJpb3JpdHlMZXZlbCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBjdXJyZW50UHJpb3JpdHlMZXZlbDtcbiAgICB9O1xuICAgIGV4cG9ydHMudW5zdGFibGVfZ2V0Rmlyc3RDYWxsYmFja05vZGUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gcGVlayh0YXNrUXVldWUpO1xuICAgIH07XG4gICAgZXhwb3J0cy51bnN0YWJsZV9uZXh0ID0gZnVuY3Rpb24gKGV2ZW50SGFuZGxlcikge1xuICAgICAgc3dpdGNoIChjdXJyZW50UHJpb3JpdHlMZXZlbCkge1xuICAgICAgICBjYXNlIDE6XG4gICAgICAgIGNhc2UgMjpcbiAgICAgICAgY2FzZSAzOlxuICAgICAgICAgIHZhciBwcmlvcml0eUxldmVsID0gMztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICBwcmlvcml0eUxldmVsID0gY3VycmVudFByaW9yaXR5TGV2ZWw7XG4gICAgICB9XG4gICAgICB2YXIgcHJldmlvdXNQcmlvcml0eUxldmVsID0gY3VycmVudFByaW9yaXR5TGV2ZWw7XG4gICAgICBjdXJyZW50UHJpb3JpdHlMZXZlbCA9IHByaW9yaXR5TGV2ZWw7XG4gICAgICB0cnkge1xuICAgICAgICByZXR1cm4gZXZlbnRIYW5kbGVyKCk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBjdXJyZW50UHJpb3JpdHlMZXZlbCA9IHByZXZpb3VzUHJpb3JpdHlMZXZlbDtcbiAgICAgIH1cbiAgICB9O1xuICAgIGV4cG9ydHMudW5zdGFibGVfcGF1c2VFeGVjdXRpb24gPSBmdW5jdGlvbiAoKSB7fTtcbiAgICBleHBvcnRzLnVuc3RhYmxlX3JlcXVlc3RQYWludCA9IGZ1bmN0aW9uICgpIHt9O1xuICAgIGV4cG9ydHMudW5zdGFibGVfcnVuV2l0aFByaW9yaXR5ID0gZnVuY3Rpb24gKHByaW9yaXR5TGV2ZWwsIGV2ZW50SGFuZGxlcikge1xuICAgICAgc3dpdGNoIChwcmlvcml0eUxldmVsKSB7XG4gICAgICAgIGNhc2UgMTpcbiAgICAgICAgY2FzZSAyOlxuICAgICAgICBjYXNlIDM6XG4gICAgICAgIGNhc2UgNDpcbiAgICAgICAgY2FzZSA1OlxuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHByaW9yaXR5TGV2ZWwgPSAzO1xuICAgICAgfVxuICAgICAgdmFyIHByZXZpb3VzUHJpb3JpdHlMZXZlbCA9IGN1cnJlbnRQcmlvcml0eUxldmVsO1xuICAgICAgY3VycmVudFByaW9yaXR5TGV2ZWwgPSBwcmlvcml0eUxldmVsO1xuICAgICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGV2ZW50SGFuZGxlcigpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgY3VycmVudFByaW9yaXR5TGV2ZWwgPSBwcmV2aW91c1ByaW9yaXR5TGV2ZWw7XG4gICAgICB9XG4gICAgfTtcbiAgICBleHBvcnRzLnVuc3RhYmxlX3NjaGVkdWxlQ2FsbGJhY2sgPSBmdW5jdGlvbiAoXG4gICAgICBwcmlvcml0eUxldmVsLFxuICAgICAgY2FsbGJhY2ssXG4gICAgICBvcHRpb25zXG4gICAgKSB7XG4gICAgICB2YXIgY3VycmVudFRpbWUgPSBleHBvcnRzLnVuc3RhYmxlX25vdygpO1xuICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIG9wdGlvbnMgJiYgbnVsbCAhPT0gb3B0aW9uc1xuICAgICAgICA/ICgob3B0aW9ucyA9IG9wdGlvbnMuZGVsYXkpLFxuICAgICAgICAgIChvcHRpb25zID1cbiAgICAgICAgICAgIFwibnVtYmVyXCIgPT09IHR5cGVvZiBvcHRpb25zICYmIDAgPCBvcHRpb25zXG4gICAgICAgICAgICAgID8gY3VycmVudFRpbWUgKyBvcHRpb25zXG4gICAgICAgICAgICAgIDogY3VycmVudFRpbWUpKVxuICAgICAgICA6IChvcHRpb25zID0gY3VycmVudFRpbWUpO1xuICAgICAgc3dpdGNoIChwcmlvcml0eUxldmVsKSB7XG4gICAgICAgIGNhc2UgMTpcbiAgICAgICAgICB2YXIgdGltZW91dCA9IC0xO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDI6XG4gICAgICAgICAgdGltZW91dCA9IDI1MDtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA1OlxuICAgICAgICAgIHRpbWVvdXQgPSAxMDczNzQxODIzO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDQ6XG4gICAgICAgICAgdGltZW91dCA9IDFlNDtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICB0aW1lb3V0ID0gNWUzO1xuICAgICAgfVxuICAgICAgdGltZW91dCA9IG9wdGlvbnMgKyB0aW1lb3V0O1xuICAgICAgcHJpb3JpdHlMZXZlbCA9IHtcbiAgICAgICAgaWQ6IHRhc2tJZENvdW50ZXIrKyxcbiAgICAgICAgY2FsbGJhY2s6IGNhbGxiYWNrLFxuICAgICAgICBwcmlvcml0eUxldmVsOiBwcmlvcml0eUxldmVsLFxuICAgICAgICBzdGFydFRpbWU6IG9wdGlvbnMsXG4gICAgICAgIGV4cGlyYXRpb25UaW1lOiB0aW1lb3V0LFxuICAgICAgICBzb3J0SW5kZXg6IC0xXG4gICAgICB9O1xuICAgICAgb3B0aW9ucyA+IGN1cnJlbnRUaW1lXG4gICAgICAgID8gKChwcmlvcml0eUxldmVsLnNvcnRJbmRleCA9IG9wdGlvbnMpLFxuICAgICAgICAgIHB1c2godGltZXJRdWV1ZSwgcHJpb3JpdHlMZXZlbCksXG4gICAgICAgICAgbnVsbCA9PT0gcGVlayh0YXNrUXVldWUpICYmXG4gICAgICAgICAgICBwcmlvcml0eUxldmVsID09PSBwZWVrKHRpbWVyUXVldWUpICYmXG4gICAgICAgICAgICAoaXNIb3N0VGltZW91dFNjaGVkdWxlZFxuICAgICAgICAgICAgICA/IChsb2NhbENsZWFyVGltZW91dCh0YXNrVGltZW91dElEKSwgKHRhc2tUaW1lb3V0SUQgPSAtMSkpXG4gICAgICAgICAgICAgIDogKGlzSG9zdFRpbWVvdXRTY2hlZHVsZWQgPSAhMCksXG4gICAgICAgICAgICByZXF1ZXN0SG9zdFRpbWVvdXQoaGFuZGxlVGltZW91dCwgb3B0aW9ucyAtIGN1cnJlbnRUaW1lKSkpXG4gICAgICAgIDogKChwcmlvcml0eUxldmVsLnNvcnRJbmRleCA9IHRpbWVvdXQpLFxuICAgICAgICAgIHB1c2godGFza1F1ZXVlLCBwcmlvcml0eUxldmVsKSxcbiAgICAgICAgICBpc0hvc3RDYWxsYmFja1NjaGVkdWxlZCB8fFxuICAgICAgICAgICAgaXNQZXJmb3JtaW5nV29yayB8fFxuICAgICAgICAgICAgKChpc0hvc3RDYWxsYmFja1NjaGVkdWxlZCA9ICEwKSwgcmVxdWVzdEhvc3RDYWxsYmFjaygpKSk7XG4gICAgICByZXR1cm4gcHJpb3JpdHlMZXZlbDtcbiAgICB9O1xuICAgIGV4cG9ydHMudW5zdGFibGVfc2hvdWxkWWllbGQgPSBzaG91bGRZaWVsZFRvSG9zdDtcbiAgICBleHBvcnRzLnVuc3RhYmxlX3dyYXBDYWxsYmFjayA9IGZ1bmN0aW9uIChjYWxsYmFjaykge1xuICAgICAgdmFyIHBhcmVudFByaW9yaXR5TGV2ZWwgPSBjdXJyZW50UHJpb3JpdHlMZXZlbDtcbiAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBwcmV2aW91c1ByaW9yaXR5TGV2ZWwgPSBjdXJyZW50UHJpb3JpdHlMZXZlbDtcbiAgICAgICAgY3VycmVudFByaW9yaXR5TGV2ZWwgPSBwYXJlbnRQcmlvcml0eUxldmVsO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIHJldHVybiBjYWxsYmFjay5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICB9IGZpbmFsbHkge1xuICAgICAgICAgIGN1cnJlbnRQcmlvcml0eUxldmVsID0gcHJldmlvdXNQcmlvcml0eUxldmVsO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH07XG4gICAgXCJ1bmRlZmluZWRcIiAhPT0gdHlwZW9mIF9fUkVBQ1RfREVWVE9PTFNfR0xPQkFMX0hPT0tfXyAmJlxuICAgICAgXCJmdW5jdGlvblwiID09PVxuICAgICAgICB0eXBlb2YgX19SRUFDVF9ERVZUT09MU19HTE9CQUxfSE9PS19fLnJlZ2lzdGVySW50ZXJuYWxNb2R1bGVTdG9wICYmXG4gICAgICBfX1JFQUNUX0RFVlRPT0xTX0dMT0JBTF9IT09LX18ucmVnaXN0ZXJJbnRlcm5hbE1vZHVsZVN0b3AoRXJyb3IoKSk7XG4gIH0pKCk7XG4iXSwibmFtZXMiOlsicGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lIiwiaXNNZXNzYWdlTG9vcFJ1bm5pbmciLCJjdXJyZW50VGltZSIsImV4cG9ydHMiLCJ1bnN0YWJsZV9ub3ciLCJzdGFydFRpbWUiLCJoYXNNb3JlV29yayIsImEiLCJpc0hvc3RDYWxsYmFja1NjaGVkdWxlZCIsImlzSG9zdFRpbWVvdXRTY2hlZHVsZWQiLCJsb2NhbENsZWFyVGltZW91dCIsInRhc2tUaW1lb3V0SUQiLCJpc1BlcmZvcm1pbmdXb3JrIiwicHJldmlvdXNQcmlvcml0eUxldmVsIiwiY3VycmVudFByaW9yaXR5TGV2ZWwiLCJiIiwiYWR2YW5jZVRpbWVycyIsImN1cnJlbnRUYXNrIiwicGVlayIsInRhc2tRdWV1ZSIsImV4cGlyYXRpb25UaW1lIiwic2hvdWxkWWllbGRUb0hvc3QiLCJjYWxsYmFjayIsInByaW9yaXR5TGV2ZWwiLCJjb250aW51YXRpb25DYWxsYmFjayIsInBvcCIsImZpcnN0VGltZXIiLCJ0aW1lclF1ZXVlIiwicmVxdWVzdEhvc3RUaW1lb3V0IiwiaGFuZGxlVGltZW91dCIsInNjaGVkdWxlUGVyZm9ybVdvcmtVbnRpbERlYWRsaW5lIiwicHVzaCIsImhlYXAiLCJub2RlIiwiaW5kZXgiLCJsZW5ndGgiLCJwYXJlbnRJbmRleCIsInBhcmVudCIsImNvbXBhcmUiLCJmaXJzdCIsImxhc3QiLCJoYWxmTGVuZ3RoIiwibGVmdEluZGV4IiwibGVmdCIsInJpZ2h0SW5kZXgiLCJyaWdodCIsImRpZmYiLCJzb3J0SW5kZXgiLCJpZCIsInRpbWVyIiwicmVxdWVzdEhvc3RDYWxsYmFjayIsImZyYW1lSW50ZXJ2YWwiLCJtcyIsImxvY2FsU2V0VGltZW91dCIsIl9fUkVBQ1RfREVWVE9PTFNfR0xPQkFMX0hPT0tfXyIsInJlZ2lzdGVySW50ZXJuYWxNb2R1bGVTdGFydCIsIkVycm9yIiwicGVyZm9ybWFuY2UiLCJub3ciLCJsb2NhbFBlcmZvcm1hbmNlIiwibG9jYWxEYXRlIiwiRGF0ZSIsImluaXRpYWxUaW1lIiwidGFza0lkQ291bnRlciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJsb2NhbFNldEltbWVkaWF0ZSIsInNldEltbWVkaWF0ZSIsIk1lc3NhZ2VDaGFubmVsIiwiY2hhbm5lbCIsInBvcnQiLCJwb3J0MiIsInBvcnQxIiwib25tZXNzYWdlIiwicG9zdE1lc3NhZ2UiLCJ1bnN0YWJsZV9JZGxlUHJpb3JpdHkiLCJ1bnN0YWJsZV9JbW1lZGlhdGVQcmlvcml0eSIsInVuc3RhYmxlX0xvd1ByaW9yaXR5IiwidW5zdGFibGVfTm9ybWFsUHJpb3JpdHkiLCJ1bnN0YWJsZV9Qcm9maWxpbmciLCJ1bnN0YWJsZV9Vc2VyQmxvY2tpbmdQcmlvcml0eSIsInVuc3RhYmxlX2NhbmNlbENhbGxiYWNrIiwidGFzayIsInVuc3RhYmxlX2NvbnRpbnVlRXhlY3V0aW9uIiwidW5zdGFibGVfZm9yY2VGcmFtZVJhdGUiLCJmcHMiLCJjb25zb2xlIiwiZXJyb3IiLCJNYXRoIiwiZmxvb3IiLCJ1bnN0YWJsZV9nZXRDdXJyZW50UHJpb3JpdHlMZXZlbCIsInVuc3RhYmxlX2dldEZpcnN0Q2FsbGJhY2tOb2RlIiwidW5zdGFibGVfbmV4dCIsImV2ZW50SGFuZGxlciIsInVuc3RhYmxlX3BhdXNlRXhlY3V0aW9uIiwidW5zdGFibGVfcmVxdWVzdFBhaW50IiwidW5zdGFibGVfcnVuV2l0aFByaW9yaXR5IiwidW5zdGFibGVfc2NoZWR1bGVDYWxsYmFjayIsIm9wdGlvbnMiLCJkZWxheSIsInRpbWVvdXQiLCJ1bnN0YWJsZV9zaG91bGRZaWVsZCIsInVuc3RhYmxlX3dyYXBDYWxsYmFjayIsInBhcmVudFByaW9yaXR5TGV2ZWwiLCJhcHBseSIsImFyZ3VtZW50cyIsInJlZ2lzdGVySW50ZXJuYWxNb2R1bGVTdG9wIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxJQUFJQSxLQUF5QixFQUFjLEVBRTFDLE1BQU07SUFDTEMseUtBQXlCO0FBQzNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGItd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZmliZXIvbm9kZV9tb2R1bGVzL3NjaGVkdWxlci9pbmRleC5qcz82YmE5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\n");

/***/ })

};
;