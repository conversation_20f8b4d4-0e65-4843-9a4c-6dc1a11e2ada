"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/traditional.mjs":
/*!**************************************************!*\
  !*** ./node_modules/zustand/esm/traditional.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createWithEqualityFn: () => (/* binding */ createWithEqualityFn),\n/* harmony export */   useStoreWithEqualityFn: () => (/* binding */ useStoreWithEqualityFn)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\n\nconst { useSyncExternalStoreWithSelector } = use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_1__;\nconst identity = (arg)=>arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n    const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getInitialState, selector, equalityFn);\n    react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n    return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn)=>{\n    const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(createState);\n    const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn)=>useStoreWithEqualityFn(api, selector, equalityFn);\n    Object.assign(useBoundStoreWithEqualityFn, api);\n    return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn)=>createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/traditional.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;